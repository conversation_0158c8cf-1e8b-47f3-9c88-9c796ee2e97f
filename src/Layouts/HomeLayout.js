'use client';

import "bootstrap/dist/css/bootstrap.min.css";
import { Fragment, useEffect } from "react";
import Header from "@/Components/UI/Header";
import Footer from "@/Components/UI/Footer";
import { LanguageProvider } from "@/context/LanguageContext";

const HomeLayout = ({ children }) => {
  return (
    <Fragment>
      <LanguageProvider>
        <Header />
        {/* Wrap main content in <main> for SEO & accessibility */}
        <main className="main-content">{children}</main>
        <Footer />
      </LanguageProvider>
    </Fragment>
  );
};

export default HomeLayout;
