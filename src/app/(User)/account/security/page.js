'use client';

import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { CheckIcon, CrossIcon, EditIcon, LogoutIcon, } from "@/assets/svgIcons/SvgIcon";
import CommonTable from "@/Components/UI/CommonTable";
import "@/css/account/Security.scss";
import "@/css/account/AccountDetails.scss";
import MetaHead from "@/Seo/Meta/MetaHead";
import TwoFactorSecurity from "./partial/TwoFactorSecurity";
import SecretQuestions from "./partial/SecretQuestions";
import Link from "next/link";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import RecentLoginActivity from "./partial/RecentLoginActivity";



export default function Security() {
    const router = useRouter();
    const [hasLoggedOut, setHasLoggedOut] = useState(false);

    const recentdata = [
        {
            site: "Tradereply.com Website",
            sitepc: "Windows PC",
            date: "Jun 16, 2024 2:06 PM",
            city: "Miami , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Android",
            date: "May 5, 2024 2:06 PM",
            city: "Florida , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Windows PC",
            date: "Jun 16, 2024 2:06 PM",
            city: "Miami , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Windows PC",
            date: "Jun 16, 2024 2:06 PM",
            city: "Miami , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Android",
            date: "May 5, 2024 2:06 PM",
            city: "Florida , United States",
        },
    ];


   

    const logoutUser = () => {
        debugger;
        Cookies.remove("authToken");
               localStorage.setItem("loggedOut", Date.now());

        // Call logout API
        fetch("/api/logout", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
        }).finally(() => {
            router.replace("/login");
        });
    };

    const metaArray = {
        noindex: true,
        title: "Account Security | Protect Your Account | TradeReply",
        description: "Secure your TradeReply.com account. Update your password, enable two-factor authentication, and monitor recent security activity.",
        canonical_link: "https://www.tradereply.com/account/security",
        og_site_name: "TradeReply",
        og_title: "Security Settings | Protect Your Account | TradeReply",
        og_description: "Enhance your account security on TradeReply. Update your password, enable two-factor authentication, and review security logs.",
        twitter_title: "Security Settings | Protect Your Account | TradeReply",
        twitter_description: "Enhance your account security on TradeReply. Update your password, enable two-factor authentication, and review security logs.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="security_sec">
                    <SidebarHeading title="Security" />
                    <Row>
                        <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                            <CommonBlackCard
                                title="Password"
                                Linktext="Update"
                                editicon={<EditIcon />}
                                className="account_card pullcontent"
                                text="We recommend updating your password periodically to prevent unauthorized access."
                            >
                                <div className="account_card_list">
                                    <ul>
                                        <li>
                                            <span>Password </span> Last Updated Mar 28, 2024
                                        </li>
                                    </ul>
                                </div>
                            </CommonBlackCard>
                        </Col>
                        <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                            <TwoFactorSecurity />

                        </Col>
                        <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                            <SecretQuestions />
                        </Col>
                        <Col xs={12} className="d-flex mb-4 mb-lg-4 ">
                            <RecentLoginActivity/>
                        </Col>
                    </Row>
                </div>
            </AccountLayout>
        </>
    )
}
