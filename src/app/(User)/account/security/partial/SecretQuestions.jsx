'use client';
import { EditIconSvg } from "@/assets/svgIcons/SvgIcon";
import { useState, useEffect } from 'react';
import { Col, Form, Row } from "react-bootstrap";
import StatusIndicator from '@/Components/UI/StatusIndicator';
import { get, post, put } from '@/utils/apiUtils';

const questionOptions = [
  'What is the first name of your oldest cousin?',
  'What was the name of your childhood best friend?',
  'What was the name of your first teacher?',
  'What was your favorite subject in high school?',
  'What was the name of your elementary school?',
  'In what city did you meet your first romantic partner?',
  'What was the make and model of your first car?',
  'What is the title of your favorite childhood book?',
  'What was the first movie you saw in a theater?',
  'What is your dream vacation destination?',
  'What is your favorite restaurant from childhood?',
  'What street did you live on in third grade?'
];


export default function SecretQuestions() {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);
  const [error, setError] = useState(null);

  const normalizeAnswer = (str) => str.trim().replace(/\s+/g, ' ');

  const [questions, setQuestions] = useState([
    { question: '', answer: '', error: '' },
    { question: '', answer: '', error: '' }
  ]);

  const [originalQuestions, setOriginalQuestions] = useState([
    { question: '', answer: '', error: '' },
    { question: '', answer: '', error: '' }
  ]);

  // Fetch secret questions from API
  const fetchSecretQuestions = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await get('/account/secret-questions');

      if (response.success && response.data) {
        const fetchedQuestions = response.data;

        // Ensure we always have exactly 2 questions
        const formattedQuestions = [
          fetchedQuestions[0] || { question: '', answer: '', error: '' },
          fetchedQuestions[1] || { question: '', answer: '', error: '' }
        ];

        setQuestions(JSON.parse(JSON.stringify(formattedQuestions)));
        setOriginalQuestions(JSON.parse(JSON.stringify(formattedQuestions)));
      }
    } catch (err) {
      console.error('Error fetching secret questions:', err);
      setError('Failed to load secret questions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSecretQuestions();
  }, []);

  const handleQuestionChange = (index, value) => {
    const updated = [...questions];
    updated[index].question = value;
    setQuestions(updated);
  };

  const handleAnswerChange = (index, value) => {
    const updated = [...questions];
    updated[index].answer = value; // no trim here
    updated[index].error = '';
    setQuestions(updated);
  };

  const validateAnswer = (answer) => {
    const trimmed = answer.trim();
    return trimmed.length >= 3 && trimmed.length <= 64;
  };

  const handleSave = async () => {
    const updated = [...questions];
    let hasError = false;

    const selectedQuestions = updated.map((q) => q.question);
    const duplicates = selectedQuestions.filter((q, i, arr) => arr.indexOf(q) !== i);

    updated.forEach((q, i) => {
        const normalizedAnswer = normalizeAnswer(q.answer);
        if (!validateAnswer(normalizedAnswer)) {
          updated[i].error = 'Please enter an answer between 3 and 64 characters.';
          hasError = true;
        } else if (duplicates.includes(q.question)) {
          updated[i].error = 'You cannot select the same question twice.';
          hasError = true;
        } else {
          updated[i].error = '';
          updated[i].answer = normalizedAnswer; // save cleaned value
        }
      });

    setQuestions(updated);

    if (!hasError) {
      try {
        setSaving(true);
        setSaveStatus('loading');
        setError(null);

        // Filter out empty questions before sending
        const questionsToSave = updated.filter(q => q.question && q.answer);

        const response = await post('/account/secret-questions', {
          questions: questionsToSave
        });

        if (response.success) {
          setSaveStatus('success');
          setIsEditing(false);
          setOriginalQuestions(JSON.parse(JSON.stringify(updated)));

          // Refresh data from server
          await fetchSecretQuestions();
        } else {
          throw new Error(response.message || 'Failed to save secret questions');
        }
      } catch (err) {
        console.error('Error saving secret questions:', err);
        const errorMessage = err.response?.data?.message || err.message || 'Failed to save secret questions';
        setSaveStatus('error');
        setError(errorMessage);
      } finally {
        setSaving(false);
        setTimeout(() => setSaveStatus(null), 3000);
      }
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset to original database state and clear all errors
    const resetQuestions = JSON.parse(JSON.stringify(originalQuestions)).map(q => ({
      ...q,
      error: ''
    }));
    setQuestions(resetQuestions);
    setError(null);
    setSaveStatus(null);
  };

  return (
    <Col lg={12} xs={12} className="mb-4 mb-lg-4">
      <div className="common_blackcard account_card">
        <div className="common_blackcard_innerheader">
          <div className="common_blackcard_innerheader_content">
            <div className="account_header_main">
              <h6>Secret Questions</h6>
              <div className="account_status_indicator">
                <StatusIndicator
                  saveStatus={saveStatus}
                  error={error}
                />
              </div>
            </div>
          </div>
          {!isEditing && (
            <div className="common_blackcard_innerheader_icon">
              <button
                className="d-flex align-items-center"
                onClick={() => setIsEditing(true)}
                disabled={saving || loading}
              >
                <EditIconSvg />
                <span className="ms-2">Update</span>
              </button>
            </div>
          )}
        </div>

        <div className="common_blackcard_innerbody">
          <div className="account_card_list">
            {loading ? (
              <div className="text-center py-3">
                <span>Loading secret questions...</span>
              </div>
            ) : error && !isEditing ? (
              <div className="text-center py-3">
                <span className="text-danger">Failed to load secret questions</span>
                <br />
                <button
                  className="btn btn-sm btn-link"
                  onClick={fetchSecretQuestions}
                  style={{ color: '#007bff', textDecoration: 'underline' }}
                >
                  Retry
                </button>
              </div>
            ) : !isEditing ? (
              <ul>
                {questions.map((q, i) => (
                  <li key={i}>
                    <span>Secret Question {i + 1} </span>{' '}
                    {q.question ? q.question : 'Not selected'}
                  </li>
                ))}
              </ul>
            ) : (
              <ul>
                {questions.map((q, index) => (
                  <li key={index}>
                    <Col xs={12} md={3}>
                      <span className="label">Secret Question {index + 1}</span>
                    </Col>
                    <Col xs={12} md={9}>
                      <div className="account_card_list_form">
                        <div className="d-flex flex-column gap-3">
                          <Form.Select
                            value={q.question}
                            onChange={(e) => handleQuestionChange(index, e.target.value)}
                            style={{
                              borderRadius: '8px',
                              height: '56px',
                              color: 'black',
                            }}
                          >
                            <option value="">Select a question</option>
                            {questionOptions.map((opt) => (
                              <option
                                key={opt}
                                value={opt}
                                disabled={questions.some((otherQ, i) => i !== index && otherQ.question === opt)}
                              >
                                {opt}
                              </option>
                            ))}
                          </Form.Select>

                          <div>
                            <Form.Control
                              type="text"
                              placeholder="Your answer"
                              value={q.answer}
                              onChange={(e) => handleAnswerChange(index, e.target.value)}
                              maxLength={64}
                              style={{
                                borderRadius: '8px',
                                height: '56px',
                                color: '#fff',
                              }}
                            />
                            {q.error && <div className="text-danger mt-1" style={{ fontSize: '14px' }}>{q.error}</div>}
                          </div>
                        </div>
                      </div>
                    </Col>
                  </li>
                ))}
              </ul>
            )}

            {isEditing && (
              <div className="account_card_list_btns mt-xxl-0 mt-3">
                <button
                  className="btn-style"
                  onClick={handleSave}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save'}
                </button>
                <button
                  className="btn-style gray-btn"
                  onClick={handleCancel}
                  disabled={saving}
                >
                  Cancel
                </button>
              </div>
            )}

            {error && !isEditing && (
              <div className="mt-3">
                <p style={{ color: 'red', fontSize: '14px' }}>
                  {error}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Col>
  );
}

