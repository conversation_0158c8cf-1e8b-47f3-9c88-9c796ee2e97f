"use client";

import React, { useEffect, useState } from "react";
import { Container, Row, Col, Form, Button } from "react-bootstrap";
import { useFormik } from "formik";
import DashboardLayout from "@/Layouts/DashboardLayout";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import toast from "react-hot-toast";
import { deleteRequest, get, post, put } from "@/utils/apiUtils";
import { ArticleSchema } from "@/validations/schema";
import JoditEditorComponent from "@/Components/UI/JoditEditorComponent";
import ListingTable from "@/Components/UI/ListingTable";
import "@/css/dashboard/StrategyManager.scss";
import dynamic from "next/dynamic";
import CustomPagination from "@/Components/UI/CustomPagination";
const Select = dynamic(() => import("react-select"), { ssr: false });

const EducationForm = () => {
  const [meta, setMeta] = useState(null);
  const [newPage, setNewPage] = useState(null);
  const [fetchCategories, setFetchCategories] = useState([]);
  const [listingEducationArticles, setListingEducationArticles] = useState([]);
  const [editingEducation, setEditingEducation] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const listingCategories = async () => {
      try {
        const response = await get("/super-admin/articles/create");
        setFetchCategories(Array.isArray(response?.data) ? response.data : []);
      } catch (error) {
        console.log(error);
      }
    };

    listingCategories();
  }, []);


  const listingAdminEducationArticles = async () => {
    const response = await get(
      "super-admin/articles/list/education",
      { page: newPage }
    );
    setListingEducationArticles(response?.data);
    setMeta(response?.pagination);
  };


  useEffect(() => {
    listingAdminEducationArticles();
  }, [newPage]);

  const handlePrimaryCategoryChange = (event) => {
    const selectedPrimaryCategory = event.target.value;
    formik.setFieldValue("primary_category_id", selectedPrimaryCategory);

    if (selectedPrimaryCategory) {
      const updatedSecondaryCategories = formik.values.secondary_categories.filter(
        (id) => String(id) !== String(selectedPrimaryCategory)
      );
      formik.setFieldValue("secondary_categories", updatedSecondaryCategories);
    }
  };

  const getFilteredSecondaryOptions = () => {
    return (fetchCategories || [])
      .filter((cat) => String(cat.id) !== String(formik.values.primary_category_id))
      .map((cat) => ({
        value: cat.id,
        label: cat.title
      }));
  };

  const getSelectedSecondaryValues = () => {
    return formik.values.secondary_categories
      .map((categoryId) => {
        const category = fetchCategories.find((cat) => String(cat.id) === String(categoryId));
        if (!category) return null;
        return { value: category.id, label: category.title };
      })
      .filter(Boolean);
  };

  const handleSecondaryCategoryChange = (selectedOptions) => {
    const selectedIds = selectedOptions.map((option) => option.value);
    formik.setFieldValue("secondary_categories", selectedIds);
  };

  const handleEdit = (educationArticles) => {
    setEditingEducation(educationArticles);

    formik.setValues({
      primary_category_id: educationArticles?.primary_category?.id || "",
      title: educationArticles?.title || "",
      summary: educationArticles?.summary || "",
      content: educationArticles?.content || "",
      secondary_categories: educationArticles?.secondary_categories?.map(cat => String(cat.id)) || [],
      feature_image: educationArticles?.feature_image || null,
    });

  };

  const formik = useFormik({
    initialValues: {
      // url_path: "",
      secondary_categories: [],
      primary_category_id: "",
      title: "",
      feature_image: "",
      content: "",
      summary: ""
    },
    validationSchema: ArticleSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: async (values, { resetForm }) => {
      setIsSubmitting(true); // disable the button
    
      try {
        const formData = new FormData();
    
        if (values.primary_category_id) {
          formData.append("primary_category_id", values.primary_category_id);
        }
    
        values.secondary_categories.forEach((id) => {
          formData.append("secondary_categories[]", id);
        });
    
        formData.append("title", values.title);
        formData.append("content", values.content);
        formData.append("summary", values.summary);
        formData.append("feature_image", values.feature_image);
        formData.append("type", "education");
    
        let response;
        if (editingEducation) {
          formData.append("_method", "PUT");
          response = await post(`/super-admin/articles/${editingEducation?.slug}`, formData);
        } else {
          response = await post("/super-admin/articles/store", formData);
        }
    
        if (response?.message) {
          toast.success(response.message);
        }
    
        resetForm(); // this clears the form fields
        setEditingEducation(null);
        listingAdminEducationArticles();
      } catch (error) {
        console.error("Error submitting form:", error);
        toast.error(error?.response?.data?.message ?? "Something went wrong. Please try again.");
      } finally {
        setIsSubmitting(false); // re-enable the button
      }
    }
    
  });

  const secondaryCategoryOptions = fetchCategories?.map((category) => ({
    value: category.id,
    label: category.title,
    database_field: category.database_field
  }));

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this item?")) return;

    try {
      const response = await deleteRequest(`/super-admin/articles/${id}`);
      if (response?.success) {
        toast.success("Education deleted successfully");
        setListingEducationArticles((prev) => prev.filter((item) => item.id !== id));
      } else {
        toast.error("Error deleting education");
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Error deleting");
    }
  };

  const handleDataFromChild = (childData) => {
    setNewPage(childData);
  };

  return (
    <DashboardLayout>
      <Container>
        <AdminHeading heading="Education" className="pt-4 pb-6" centered />
        <Form onSubmit={formik.handleSubmit}>

          <Row>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Primary Category</Form.Label>
                <Form.Select
                  name="primary_category_id"
                  value={formik.values.primary_category_id}
                  onChange={handlePrimaryCategoryChange}
                >
                  <option value="">Select a category</option>
                  {fetchCategories?.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.title}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label>Secondary Categories</Form.Label>
                <Select
                  isMulti
                  name="secondary_categories"
                  options={getFilteredSecondaryOptions()} // ✅ Uses function to filter options
                  value={getSelectedSecondaryValues()} // ✅ Uses function to format selected values
                  onChange={handleSecondaryCategoryChange} // ✅ Uses function to update values
                  isDisabled={!formik.values.primary_category_id} // ✅ Disable if no primary is selected
                  classNamePrefix="select"
                  styles={{
                    control: (provided) => ({
                      ...provided,
                      backgroundColor: "white",
                      color: "black",
                      borderColor: "#ccc",
                    }),
                    menu: (provided) => ({
                      ...provided,
                      backgroundColor: "white",
                      color: "black",
                    }),
                    option: (provided, state) => ({
                      ...provided,
                      backgroundColor: state.isSelected ? "#f0f0f0" : "white",
                      color: "black",
                      "&:hover": {
                        backgroundColor: "#e6e6e6",
                      },
                    }),
                  }}
                />
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Page Title</Form.Label>
                <Form.Control
                  type="text"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.title && formik.errors.title}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.title}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={6} className="mt-3">
              <Form.Group>
                <Form.Label>Header Image Filename (936x410px) - Serves CDN /education/featured/</Form.Label>
                <Form.Control
                  type="text"
                  name="feature_image"
                  value={formik.values.feature_image}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.feature_image && formik.errors.feature_image}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.feature_image}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Page Summary</Form.Label>
                <Form.Control
                  as="textarea"
                  name="summary"
                  maxLength={250}
                  value={formik.values.summary}
                  onChange={formik.handleChange}
                  isInvalid={formik.touched.summary && formik.errors.summary}
                />
                <Form.Control.Feedback type="invalid" className="text-white">
                  {formik.errors.summary}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={12} className="mt-3">
              <Form.Group>
                <Form.Label>Body Text</Form.Label>
                <div className="editor-container">
                  <JoditEditorComponent
                    value={formik.values.content}
                    onChange={(newValue) =>
                      formik.setFieldValue("content", newValue)
                    }
                  />
                </div>
                {formik.touched.content && formik.errors.content && (
                  <div className="text-white">{formik.errors.content}</div>
                )}
              </Form.Group>
            </Col>
          </Row>

          {/* Submit Button */}
          <Button type="submit" className="mt-4" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : editingEducation ? "Update" : "Submit"}
          </Button>

        </Form>

        <div className="trade_manager_entrylist">
          <ListingTable
            array={listingEducationArticles}
            onUpdate={handleEdit}
            onDelete={handleDelete}
          />
        </div>

        <div className="d-flex justify-content-end mt-3">
          <CustomPagination
            useLinks={false}
            links={meta}
            onDataSend={handleDataFromChild}
            pageUrl={"super-admin/education/"}
          />
        </div>
      </Container>
    </DashboardLayout>
  );
};

export default EducationForm;
