import HomeLayout from "@/Layouts/HomeLayout";
import { Container } from "react-bootstrap";
import CategoryContent from "./components/CategoryContent";
import { cookies } from "next/headers";

async function fetchCategoryData(slug, searchKeyword = "") {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/category?slug=${slug}&key=${searchKeyword}`, { cache: "no-store" });
  if (!res.ok) throw new Error(`API error: ${res.status}`);
  return res.json();
}
export default async function Category({ params }) {
  const slug = params?.detail || "";
  const page = parseInt(params?.id || "1",);
  const cookieStore = cookies();
  const key = cookieStore.get("category-id")?.value || ""; //
  const totalPagesFromClient = cookieStore.get("total-pages")?.value;

  const isSearch = key?.trim() !== "";
  const categoryData = await fetchCategoryData(slug);
  // const totalPages = categoryData?.data?.meta?.total || 1;
  console.log("totalPagesFromClient",totalPagesFromClient)
  const selectedCategory = categoryData?.data?.selected_category || {};
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || "production"; // Ensure fallback

  function truncateText(text, maxLength = 160) {
    if (!text || typeof text !== "string") return "";

    if (text.length <= maxLength) return text;

    let cutoff = maxLength - 3;
    if (text[cutoff] === " ") {
      return text.substring(0, cutoff) + "...";
    } else {
      while (cutoff > 0 && text[cutoff] !== " ") {
        cutoff--;
      }
      return text.substring(0, cutoff) + "...";
    }
  }

  const description = truncateText(selectedCategory?.summary || selectedCategory?.content);


  const canonicalLink = page === 1
    ? `https://www.tradereply.com/category${slug ? `/${slug}` : ""}`
    : `https://www.tradereply.com/category${slug ? `/${slug}` : ""}/page/${page}`;

  const relNextLink = page < totalPagesFromClient
    ? `https://www.tradereply.com/category${slug ? `/${slug}` : ""}/page/${page + 1}`
    : null;

  const metaArray = {
    title:  `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
    description: description,
    og_title:  `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
    og_description: description,
    og_site_name: "TradeReply",
    twitter_title:  `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
    twitter_description: description,
    noindex: isSearch,
    canonical_link: canonicalLink,
    rel_next: relNextLink,
  };

  const articles = categoryData?.data?.articles || [];

  const collectionPageSchema = !isSearch ? {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": selectedCategory?.title || "TradeReply Category",
    "description": selectedCategory?.summary || selectedCategory?.content || "",
    "url": canonicalLink,
    "itemListElement": articles.slice(0, 10).map((article, index) => ({
      "@type": "ListItem",
      position: index + 1,
      url: `https://www.tradereply.com/article/${article?.slug}`
    }))
  } : null;

  const breadcrumbListSchema = !isSearch ? {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        position: 1,
        name: "Home",
        item: "https://www.tradereply.com/"
      },
      {
        "@type": "ListItem",
        position: 2,
        name: "Category",
        item: "https://www.tradereply.com/category/"
      },
      {
        "@type": "ListItem",
        position: 3,
        name: selectedCategory?.title || "Category",
        item: `https://www.tradereply.com/category/${slug}/`
      },
      {
        "@type": "ListItem",
        position: 4,
        name: `Page ${page}`,
        item: canonicalLink
      }
    ]
  } : null;

  return (
    <>
      {!isSearch && (
        <>
          {collectionPageSchema && (
            <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(collectionPageSchema) }} />
          )}
          {breadcrumbListSchema && (
            <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbListSchema) }} />
          )}
        </>
      )}
      <CategoryContent
        initialListingAllCategories={categoryData?.data?.allcategories}
        initialAllCategoryArticles={categoryData?.data?.articles}
        initialCategoryMeta={categoryData?.data?.meta}
        initialSelectedCategory={categoryData?.data?.selected_category}
        currentPage={page}
        keyWord={key}
        metaArray={metaArray} 
      />
    </>
  );

}

// export async function generateMetadata({ params }) {
//   const slug = params?.detail || "";
//   const categoryData = await fetchCategoryData(slug);
//   const selectedCategory = categoryData?.data?.selected_category || {};
//   const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || "production"; // Ensure fallback

//   function truncateText(text, maxLength = 160) {
//     if (!text || typeof text !== "string") return "";

//     if (text.length <= maxLength) return text;

//     let cutoff = maxLength - 3;
//     if (text[cutoff] === " ") {
//       return text.substring(0, cutoff) + "...";
//     } else {
//       while (cutoff > 0 && text[cutoff] !== " ") {
//         cutoff--;
//       }
//       return text.substring(0, cutoff) + "...";
//     }
//   }

//   const description = truncateText(selectedCategory?.summary || selectedCategory?.content);

//   return {
//     title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
//     description,
//     openGraph: {
//       title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
//       description,
//       images: [{
//         url: "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
//         width: 1200,
//         height: 630,
//       }],
//     },
//     twitter: {
//       title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
//       description,
//       site: "@JoinTradeReply",
//       images: ["https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png"],
//     },
//     icons: {
//       icon: [
//         {
//           url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
//           type: "image/x-icon",
//         },
//         {
//           url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
//           type: "image/svg+xml",
//         },
//       ],
//     },
//   };
// }







