import CategoryClient from "./CategoryClient";
import { cookies } from "next/headers";

export default async function CategoryPage({ params, searchParams }) {
  const slug = searchParams?.slug || null;
  const page = parseInt(params?.id) || 1;
  const cookieStore = cookies();
  const key = cookieStore.get("categorySearchKey")?.value || "";

  const canonicalLink =
    page === 1
      ? `https://www.tradereply.com/category`
      : `https://www.tradereply.com/category/page/${page}`;

  let data = {
    allcategories: [],
    articles: [],
    meta: {},
    selected_category: null,
  };

  let categoryPagination = {};
  let nextLink = null;

  try {
    const query = new URLSearchParams({ slug, key, page }).toString();
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    const res = await fetch(`${apiBase}/category?${query}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      throw new Error("Failed to fetch category data");
    }

    const response = await res.json();
    data = response.data;
    console.log("API RESPONSE (server):", JSON.stringify(response?.data, null, 2));

    categoryPagination = data.meta;
    if (categoryPagination?.current_page < categoryPagination?.total) {
      nextLink = `https://www.tradereply.com/category/page/${categoryPagination.current_page + 1}`;
    }

  } catch (error) {
    console.error("Failed to fetch category data:", error);
  }

  const isSearch = key?.trim() !== "";

  const metaArray = {
    title: "TradeReply Categories | Explore Trading Content",
    description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
    og_title: "TradeReply Categories | Explore Trading Content",
    og_description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
    og_site_name: "TradeReply",
    twitter_title: "TradeReply Categories | Explore Trading Content",
    twitter_description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
    noindex: isSearch,
    ...(isSearch ? {} : { canonical_link: canonicalLink }),
    rel_next: nextLink,
  };

  return (
    <CategoryClient
      initialData={data}
      slug={slug}
      keyWord={key}
      currentPage={page}
      metaArray={metaArray}
    />
  );
}
