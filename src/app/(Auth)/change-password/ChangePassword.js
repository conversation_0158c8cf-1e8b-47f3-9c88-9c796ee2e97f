"use client";
import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Formik, Field, Form, ErrorMessage } from "formik";
import { changePasswordSchema } from "@/validations/schema";
import AuthLogo from "@/Components/common/AuthLogo";
import TextInput from "@/Components/UI/TextInput";
import AuthLayout from "@/Layouts/AuthLayout";
import { resetPassword } from "@/utils/auth";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import InputError from "@/Components/UI/InputError";
import Link from "next/link";
import CommonButton from "@/Components/UI/CommonButton";
import LoginFooter from "@/Components/UI/LoginFooter";
import AlertMsg from "@/Components/UI/AlertMsg";
import MetaHead from "@/Seo/Meta/MetaHead";
import PasswordValidation from "@/Components/common/Auth/PasswordValidation";
import toast from "react-hot-toast";

const initialValues = {
    new_password: "",
    confirm_new_password: ""
};

export default function ChangePassword({ status }) {

    const router = useRouter();

    const [showPasswordCheck, setShowPasswordCheck] = useState(false);
    const [password, setPassword] = useState("");
    const [isFocused, setIsFocused] = useState(false);
    const [isSessionValid, setIsSessionValid] = useState(null);




    const dataType = "reset_password_data";


    const expiredOnceRef = useRef(false);



    // useEffect(() => {

    //     const checkSessionValidity = () => {
    //         const savedData = JSON.parse(sessionStorage.getItem(dataType));
    //         if (!savedData && !expiredOnceRef.current) {
    //             expiredOnceRef.current = true;
    //             router.replace("/login");
    //             return false;
    //         }

    //         const expired = savedData && Date.now() > savedData.expiresAt;

    //         if (expired && !expiredOnceRef.current) {
    //             expiredOnceRef.current = true;
    //             sessionStorage.removeItem(dataType);
    //             sessionStorage.removeItem("masked_email");
    //             sessionStorage.removeItem("identifier_type");

    //             sessionStorage.setItem("sessionExpired", "true");
    //             // toast.error("Session expired. Please request a new code.");
    //             router.replace(resetPassword ? "/locate-account" : "/login");
    //             return false;
    //         }

    //         return true;
    //     };

    //     if (checkSessionValidity()) {
    //         setIsSessionValid(true);

    //         const interval = setInterval(() => {
    //             checkSessionValidity();
    //         }, 5000);

    //         return () => clearInterval(interval);
    //     }
    // }, []);

    const submit = async (values, { setSubmitting, setErrors }) => {
        setSubmitting(true);

        const response = await resetPassword(values.new_password);

        if (response.success) {
            sessionStorage.removeItem("reset_password_data");
            router.push("/login");
        } else {
            setErrors({ new_password: response.message || "Error resetting password" });
        }

        setSubmitting(false);
    };

    const metaArray = {
        noindex: true,
        title: "Change Password | Secure Your TradeReply Account",
        description: "Update your TradeReply password securely to protect your account. Keep your trading data safe with a strong, new password.",
        canonical_link: "https://www.tradereply.com/change-password",
        og_site_name: "TradeReply",
        og_title: "Change Password | Secure Your TradeReply Account",
        og_description: "Update your TradeReply password securely to protect your account. Keep your trading data safe with a strong, new password.",
        twitter_title: "Change Password | Secure Your TradeReply Account",
        twitter_description: "Update your TradeReply password securely to protect your account. Keep your trading data safe with a strong, new password.",
    };

    // if (isSessionValid === null) {
    //     return <div className="loading-screen">Checking session...</div>;
    // }

    if (isSessionValid === false) {
        return null;
    }
    return (
        <AuthLayout>
            <MetaHead props={metaArray} />
            <div className="loginCommon_rightSide">
                <div className="loginCommon_rightSide_inner">
                    <div className="loginCommon_rightSide_formBox">
                        <AuthLogo />
                        <div className="loginHeading">
                            <h1>Change Your Password</h1>
                        </div>
                        <div className="text-center pt-3 pb-4">
                            <span>For your security, we highly recommend choosing a
                                unique password that is not used elsewhere.</span>
                        </div>
                        <div className="loginTabs">
                            <div className="loginForm">
                                <AlertMsg message={status} />
                                <Formik
                                    initialValues={initialValues}
                                    validationSchema={changePasswordSchema}
                                    onSubmit={submit}
                                >
                                    {({
                                        handleChange,
                                        errors,
                                        touched,
                                        isSubmitting,
                                        submitCount,
                                        dirty,
                                        isValid,
                                        values
                                    }) => (
                                        <Form>
                                            {/* New Password */}
                                            <div className="authCorrectIcon">
                                                <div className="checkIcon">
                                                    {values.new_password && !errors.new_password && <CheckIcon width="25" height="25" />}
                                                </div>
                                                <Field name="new_password">
                                                    {({ field, meta, form: { touched, errors } }) => (
                                                        <TextInput
                                                            {...field}
                                                            placeholder="Enter New Password"
                                                            type="password"
                                                            maxLength={64}
                                                            onChange={(e) => {
                                                                const cleanedValue = e.target.value.replace(/\s/g, "");
                                                                field.onChange({ target: { name: e.target.name, value: cleanedValue } });
                                                                setPassword(cleanedValue);
                                                                setShowPasswordCheck(true);
                                                                setIsFocused(false);
                                                            }}
                                                            onBlur={(e) => {
                                                                field.onBlur(e);
                                                                if (e.target.value.replace(/\s/g, "").length === 0) {
                                                                    setShowPasswordCheck(false);
                                                                    setIsFocused(false);
                                                                }
                                                            }}
                                                            onFocus={() => {
                                                                setShowPasswordCheck(true);
                                                            }}
                                                            error={
                                                                submitCount > 0 && errors.new_password ? <InputError message={errors.new_password} /> : null
                                                            }
                                                            isError={submitCount > 0 && errors.new_password ? true : false}
                                                        />
                                                    )}
                                                </Field>
                                                {showPasswordCheck && !(submitCount > 0 && errors.new_password) && (
                                                    <PasswordValidation
                                                        isValid={touched.new_password && !errors.new_password}
                                                        password={password}
                                                    />
                                                )}
                                            </div>
                                            {/* Confirm New Password */}
                                            <div className="authCorrectIcon">
                                                <div className="checkIcon">
                                                    {values.confirm_new_password && !errors.confirm_new_password && <CheckIcon width="25" height="25" />}
                                                </div>
                                                <Field name="confirm_new_password">
                                                    {({ field }) => (
                                                        <TextInput
                                                            {...field}
                                                            placeholder="Re-enter New Password"
                                                            type="password"
                                                            maxLength={64}
                                                            error={
                                                                values.confirm_new_password && errors.confirm_new_password ? (
                                                                    <InputError message={errors.confirm_new_password} />
                                                                ) : null
                                                            }
                                                            isError={values.confirm_new_password && errors.confirm_new_password ? true : false}

                                                        />
                                                    )}
                                                </Field>
                                            </div>
                                            <div className="w-100">
                                                <CommonButton
                                                    type="submit"
                                                    title="Change Password"
                                                    fluid
                                                // disabled={!(dirty && isValid) || isSubmitting}
                                                />
                                            </div>

                                            <Link href={"/login"} className="w-100 mt-3">
                                                <CommonButton
                                                    type="submit"
                                                    title="Cancel"
                                                    white20
                                                />
                                            </Link>
                                        </Form>
                                    )}
                                </Formik>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 mt-md-5">
                        <LoginFooter />
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}
