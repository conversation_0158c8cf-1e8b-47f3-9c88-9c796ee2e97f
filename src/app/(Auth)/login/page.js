"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { Formik, Field, Form } from "formik";
import Link from "next/link";
import Cookies from "js-cookie";
import ReCAPTCHA from "react-google-recaptcha";
import TextInput from "@/Components/UI/TextInput";
import AuthLayout from "@/Layouts/AuthLayout";
import InputError from "@/Components/UI/InputError";
import CommonButton from "@/Components/UI/CommonButton";
import { loginSchema } from "@/validations/schema";
import { RightArrowIconSvg, CheckIcon, RedErrorCircle } from "@/assets/svgIcons/SvgIcon";
import ThirdPartyLogin from "@/Components/common/ThirdPartyLogin";
import LoginFooter from "@/Components/UI/LoginFooter";
import NavLink from "@/Components/UI/NavLink";
import AlertMsg from "@/Components/UI/AlertMsg";
import { loginUser } from "../../../redux/authSlice";
import MetaHead from "@/Seo/Meta/MetaHead";
import AuthLogo from "@/Components/common/AuthLogo";

const initialValues = {
  email: "",
  password: "",
};

export default function Login({ status }) {
  const dispatch = useDispatch();
  const router = useRouter();
  const { token } = useSelector((state) => state?.auth || {});
  const [authError, setAuthError] = useState(null);
  const [captchaToken, setCaptchaToken] = useState(null);
  const [showSessionError, setShowSessionError] = useState(false);
  const [isCaptchaEnabled, setIsCaptchaEnabled] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setIsCaptchaEnabled(sessionStorage.getItem("captcha_required") === "true");
    }
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const tokenFromCookies = Cookies.get("authToken");
     
      if (!tokenFromCookies && sessionStorage.getItem("sessionExpired") === "true") {
        setShowSessionError(true);
         sessionStorage.removeItem("sessionExpired");
         setTimeout(() => {
            setShowSessionError(false);
          }, 3000);
        
      } else {
        setShowSessionError(false); 
      }
    }
  }, []);







  const handleLoginSubmit = async (values, { setSubmitting, setErrors, resetForm }) => {
    setAuthError(null);

    if (isCaptchaEnabled && !captchaToken) {
      setAuthError("Please complete the reCAPTCHA.");
      setSubmitting(false);
      return;
    }

    try {

      const response = await dispatch(
        loginUser({ ...values, captchaToken: isCaptchaEnabled ? captchaToken : null })
      );

      sessionStorage.removeItem("sessionExpired");

      if (response?.payload?.lockout_redirect && response.payload?.redirect_to) {
        localStorage.setItem("lockout_redirect_message", response?.payload?.message);
        router.push(response?.payload?.redirect_to || "/locate-account");
        return;
      }

      if (response.payload?.errors && Object.keys(response.payload.errors).length > 0) {
        setErrors(response.payload.errors);
        setAuthError(response.payload?.message || "Login failed.");
      } else if (response.payload?.success) {

        // console.log("payload:", response.payload);
        // console.log("user:", response.payload?.data?.user);
        // console.log("subscription:", response.payload?.data?.user?.subscription_id);

        if (response.payload?.data?.user?.subscription_id === 1) {
          Cookies.set("returning_login", "true");
          router.push('/pricing?source=free_login_upgrade&feature=buy_trial');
          // router.push("/checkout")

        }
        else {
          router.push("/dashboard?source=member_login_default");
        }

      } else {
        setAuthError(response.payload?.message || "Something went wrong.");
        resetForm();

        if (response.payload?.captcha_required) {
          setIsCaptchaEnabled(true);
        } else {
          setIsCaptchaEnabled(false);
        }

      }
    } catch (error) {
      setAuthError(error.message || "An unexpected error occurred. Please try again.");
    }

    setSubmitting(false);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("masked_email");
      sessionStorage.removeItem("identifier_type");
      sessionStorage.removeItem("signup_data");
      sessionStorage.removeItem("reset_password_data");

      const tokenFromCookies = Cookies.get("authToken");
      if (tokenFromCookies) {
        router.push("/dashboard");
      }
    }
  }, [token, router]);


  useEffect(() => {
    if (authError) {
      const timer = setTimeout(() => {
        setAuthError(null);
      }, 10000);
      return () => clearTimeout(timer);
    }
  }, [authError]);

  const metaArray = {
    noindex: true,
    title: "Log In to TradeReply | Access Your Trading Dashboard",
    description: "Log in to TradeReply.com to access your personalized trading dashboard, manage trades, and optimize your trading strategies with real-time insights.",
    canonical_link: "https://www.tradereply.com/login",
    og_site_name: "TradeReply",
    og_title: "Log In to TradeReply | Access Your Trading Dashboard",
    og_description: "Log in to TradeReply.com to access your personalized trading dashboard, manage trades, and optimize your trading strategies with real-time insights.",
    twitter_title: "Log In to TradeReply | Access Your Trading Dashboard",
    twitter_description: "Log in to TradeReply.com to access your personalized trading dashboard, manage trades, and optimize your trading strategies with real-time insights.",
  };

  return (
    <AuthLayout>
      <MetaHead props={metaArray} />
      <div className="loginCommon_rightSide">
        <div className="loginCommon_rightSide_inner">
          <div className="backbtn">
            <NavLink href={"/"}>
              <RightArrowIconSvg color="svg-white_baseblue" /> Return to Home
            </NavLink>
          </div>
          {showSessionError && (
            <div className="d-flex justify-content-center">
              <div className="session-expire-message">
                <span>Your session has expired. Please start again.</span>
              </div>
            </div>
          )}
          <div className="loginCommon_rightSide_formBox">
            <AuthLogo />
            <div className="loginHeading">
              <h1>Log in with</h1>
            </div>
            <ThirdPartyLogin />
            <div className="orLine">
              <span>or continue with</span>
            </div>
            <div className="loginTabs">
              <div className="loginForm">
                <AlertMsg message={status} />
                <Formik
                  initialValues={initialValues}
                  validationSchema={loginSchema}
                  onSubmit={handleLoginSubmit}
                >
                  {({ touched, errors, isSubmitting, submitCount, values, isValid, dirty }) => (
                    <Form>
                      <div className="authCorrectIcon">
                        <div className="checkIcon">
                          {values.email && !errors.email && dirty && <CheckIcon width="25" height="25" />}
                        </div>
                        <Field name="email">
                          {({ field }) => (
                            <TextInput
                              {...field}
                              placeholder="Email"
                              type="text"
                              maxLength={100}
                              error={submitCount > 0 && errors.email ? <InputError message={errors.email} /> : null}
                              isError={submitCount > 0 && errors.email}
                            />
                          )}
                        </Field>
                      </div>
                      <Field name="password">
                        {({ field }) => (
                          <TextInput
                            {...field}
                            placeholder="Password"
                            type="password"
                            maxLength={64}
                            error={
                              submitCount > 0 && errors.password ? <InputError message={errors.password} /> : null
                            }
                            isError={submitCount > 0 && errors.password}
                          />
                        )}
                      </Field>
                      <div className="Forgotpassoword text-center pt-2 mb-3">
                        <Link href="/locate-account" prefetch={true}>
                          Forgot password or can&apos;t log in
                        </Link>
                      </div>

                      {/* Conditionally show reCAPTCHA */}
                      {isCaptchaEnabled && (
                        <div className="d-flex justify-content-center mb-3">
                          <div className="recaptcha-container">
                            <ReCAPTCHA
                              sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
                              onChange={(token) => setCaptchaToken(token)}
                            />
                          </div>
                        </div>
                      )}

                      <div className="w-100">
                        <CommonButton
                          type="submit"
                          title={isSubmitting ? "Loading" : "Log In"}
                          fluid
                          disabled={isSubmitting}
                        />
                      </div>
                      <div className="anAccount mt-3 text-center">
                        <h6>
                          <Link href="/signup" prefetch={true}>
                            Create a free TradeReply Account
                          </Link>
                        </h6>
                      </div>
                      {authError && (
                        <div className="d-flex justify-content-center">
                          <div className="invalid_credential mt-3">
                            <RedErrorCircle />
                            <span>{authError}</span>
                          </div>
                        </div>
                      )}
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
          <div className="mt-4 mt-md-5">
            <LoginFooter />
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}