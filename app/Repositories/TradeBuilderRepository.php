<?php

namespace App\Repositories;

use App\Models\TradeBuilder;
use App\Models\FieldDefinition;
use App\Models\TradeBuilderForm;
use App\Traits\TradeBuilderFieldsTrait;
use App\Models\TransactionFieldDefinition;
use App\Models\PortfolioFieldDefinition;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;

class TradeBuilderRepository
{
    use TradeBuilderFieldsTrait;

    /**
     * Helper to order fields according to reference.
     */
    private function orderFieldsByReference(array $dataList, array $referenceOrder): array
    {
        $orderedList    = [];
        $remainingItems = $dataList;

        foreach ($referenceOrder as $refField) {
            foreach ($dataList as $key => $item) {
                if (strtoupper($item['database_field']) === strtoupper($refField)) {
                    $orderedList[] = $item;
                    unset($remainingItems[$key]);
                    break;
                }
            }
        }

        return array_merge($orderedList, $remainingItems);
    }

    /**
     * Fetch Transaction Fields from `TransactionFieldDefinition` table.
     *
     * @param array $transactionFields
     * @param array $fieldOrder
     * @return array
     */
    private function getTransactionFields(array $transactionFields, array $fieldOrder): array
    {
        $data = TransactionFieldDefinition::query()
            ->whereIn('transaction_field_definitions.database_field', $transactionFields)
            ->join('field_definitions', 'transaction_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'transaction_field_definitions.database_field',
                'transaction_field_definitions.summary',
                'transaction_field_definitions.account_field',
                'transaction_field_definitions.account_field_value'
            ])
            ->map(function ($item) {
                $item->expected_values = !empty($item->expected_values)
                    ? array_map('trim', explode(',', $item->expected_values))
                    : [];
                return $item;
            })
            ->keyBy('database_field')
            ->toArray();

        $orderedData = [];
        foreach ($fieldOrder as $field) {
            if (isset($data[$field])) {
                $orderedData[] = $data[$field];
            }
        }

        return $orderedData;
    }

    /**
     * Fetch Portfolio Fields from `PortfolioFieldDefinition` table.
     *
     * @param array $portfolioFields
     * @param array $fieldOrder
     * @return array
     */
    private function getPortfolioFields(array $portfolioFields, array $fieldOrder): array
    {
        $data = PortfolioFieldDefinition::query()
            ->with(['userFieldValue' => function ($query) {
                $query->where('user_id', auth()->id());
            }, 'fieldDefinition'])
            ->whereIn('portfolio_field_definitions.database_field', $portfolioFields)
            ->join('field_definitions', 'portfolio_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'portfolio_field_definitions.database_field',
                'portfolio_field_definitions.summary',
                'portfolio_field_definitions.account_field',
                'portfolio_field_definitions.account_field_value',
                'portfolio_field_definitions.id as id',
            ])
            ->map(function ($item) {
                $item->expected_values = !empty($item->expected_values)
                    ? array_map('trim', explode(',', $item->expected_values))
                    : [];
                $item->portfolioValue  = $item->userFieldValue->value ?? 0;
                return $item;
            })
            ->keyBy('database_field')
            ->toArray();

        $orderedData = [];
        foreach ($fieldOrder as $field) {
            if (isset($data[$field])) {
                $orderedData[] = $data[$field];
            }
        }

        return $orderedData;
    }

    public function getTradeReplayFormulas(): array
    {
        $jsonFile = public_path('TradeReply_Formulas.json');

        if (!File::exists($jsonFile)) {
            throw new \Exception("TradeReply_Formulas.json not found.");
        }

        return json_decode(File::get($jsonFile), true) ?? [];
    }

    /**
     * Check if a database field is editable based on `usr` in JSON formulas.
     *
     * @param array $item
     * @param array $formulaData
     * @return array
     */
    public function applyIsEditable(array $item, array $formulaData): array
    {
        $dbField    = $item['database_field'] ?? '';
        $datatype   = null;
        $hasFormula = false;

        foreach ($formulaData as $record) {
            $scopes     = $record['SCOPES'] ?? [];
            $scopeKey   = str_starts_with($dbField, 'transaction_') ? 'TRANSACTION' : 'PORTFOLIO';
            $tradeScope = $scopes[$scopeKey] ?? [];

            if (isset($tradeScope['DATABASE FIELD']) && $tradeScope['DATABASE FIELD'] === $dbField) {
                $datatype = $record['DATATYPE'] ?? null;

                for ($i = 1; $i <= 5; $i++) {
                    if (!empty($tradeScope['FORMULA']["f$i"]) && $tradeScope['FORMULA']["f$i"] == 1) {
                        $hasFormula = true;
                        break;
                    }
                }

                if (isset($tradeScope['FORMULA']['usr']) && $tradeScope['FORMULA']['usr'] == 1) {
                    return array_merge($item, [
                        'is_editable' => true,
                        'datatype'    => $datatype,
                        'has_formula' => $hasFormula
                    ]);
                }
            }
        }

        return array_merge($item, [
            'is_editable' => false,
            'datatype'    => $datatype,
            'has_formula' => $hasFormula
        ]);
    }

    /**
     * Get Default Fields (Merged Transaction & Portfolio Fields).
     *
     * @return array
     * @throws \Exception
     */
    public function getEntryOverviewFields(): array
    {
        $transactionFields = array_filter($this->entryOverviewFields, fn($field) => str_starts_with($field, 'transaction_'));
        $portfolioFields   = array_filter($this->entryOverviewFields, fn($field) => str_starts_with($field, 'portfolio_'));

        $transactionData = $this->getTransactionFields($transactionFields, $this->entryOverviewFields);
        $portfolioData   = $this->getPortfolioFields($portfolioFields, $this->entryOverviewFields);

        $formulaData = $this->getTradeReplayFormulas();

        $mergedData = [];
        foreach ($this->entryOverviewFields as $field) {
            $portfolioItem = collect($portfolioData)->firstWhere('database_field', $field);
            if ($portfolioItem) {
                $mergedData[] = $this->applyIsEditable($portfolioItem, $formulaData);
                continue;
            }
            $transactionItem = collect($transactionData)->firstWhere('database_field', $field);
            if ($transactionItem) {
                $mergedData[] = $this->applyIsEditable($transactionItem, $formulaData);
            }
        }

        return $mergedData;
    }

    /**
     * Get Exit Overview Fields (Merged Transaction & Portfolio Fields)
     *
     * @return array
     */
    public function getExitOverviewFields(): array
    {
        $transactionFields = array_filter($this->exitOverviewFields, fn($field) => str_starts_with($field, 'transaction_'));
        $portfolioFields   = array_filter($this->exitOverviewFields, fn($field) => str_starts_with($field, 'portfolio_'));

        $transactionData = $this->getTransactionFields($transactionFields, $this->exitOverviewFields);
        $portfolioData   = $this->getPortfolioFields($portfolioFields, $this->exitOverviewFields);

        $formulaData = $this->getTradeReplayFormulas();

        $mergedData = [];
        foreach ($this->exitOverviewFields as $field) {
            $portfolioItem = collect($portfolioData)->firstWhere('database_field', $field);
            if ($portfolioItem) {
                $mergedData[] = $this->applyIsEditable($portfolioItem, $formulaData);
                continue;
            }
            $transactionItem = collect($transactionData)->firstWhere('database_field', $field);
            if ($transactionItem) {
                $mergedData[] = $this->applyIsEditable($transactionItem, $formulaData);
            }
        }

        return $mergedData;
    }

    /**
     * Get Entry Projection Fields (Merged Transaction & Portfolio Fields)
     *
     * @return array
     */
    public function getEntryProjectionFields(): array
    {
        $transactionFields = array_filter($this->projectionFields['entry'], fn($field) => str_starts_with($field, 'transaction_'));
        $portfolioFields   = array_filter($this->projectionFields['entry'], fn($field) => str_starts_with($field, 'portfolio_'));

        $transactionData = $this->getTransactionFields($transactionFields, $this->projectionFields['entry']);
        $portfolioData   = $this->getPortfolioFields($portfolioFields, $this->projectionFields['entry']);

        $formulaData = $this->getTradeReplayFormulas();

        $mergedData = [];
        foreach ($this->projectionFields['entry'] as $field) {
            $portfolioItem = collect($portfolioData)->firstWhere('database_field', $field);
            if ($portfolioItem) {
                $mergedData[] = $this->applyIsEditable($portfolioItem, $formulaData);
                continue;
            }
            $transactionItem = collect($transactionData)->firstWhere('database_field', $field);
            if ($transactionItem) {
                $mergedData[] = $this->applyIsEditable($transactionItem, $formulaData);
            }
        }

        return $mergedData;
    }

    /**
     * Get Exit Projection Fields (Merged Transaction & Portfolio Fields)
     *
     * @return array
     */
    public function getExitProjectionFields(): array
    {
        $transactionFields = array_filter($this->projectionFields['exit'], fn($field) => str_starts_with($field, 'transaction_'));
        $portfolioFields   = array_filter($this->projectionFields['exit'], fn($field) => str_starts_with($field, 'portfolio_'));

        $transactionData = $this->getTransactionFields($transactionFields, $this->projectionFields['exit']);
        $portfolioData   = $this->getPortfolioFields($portfolioFields, $this->projectionFields['exit']);

        $formulaData = $this->getTradeReplayFormulas();

        $mergedData = [];
        foreach ($this->projectionFields['exit'] as $field) {
            $portfolioItem = collect($portfolioData)->firstWhere('database_field', $field);
            if ($portfolioItem) {
                $mergedData[] = $this->applyIsEditable($portfolioItem, $formulaData);
                continue;
            }
            $transactionItem = collect($transactionData)->firstWhere('database_field', $field);
            if ($transactionItem) {
                $mergedData[] = $this->applyIsEditable($transactionItem, $formulaData);
            }
        }

        return $mergedData;
    }

    /**
     * Get Entry Outcome Fields (Merged Transaction & Portfolio Fields)
     *
     * @return array
     */
    public function getEntryOutcomeFields(): array
    {
        $transactionFields = array_filter($this->outcomeFields['entry'], fn($field) => str_starts_with($field, 'transaction_'));
        $portfolioFields   = array_filter($this->outcomeFields['entry'], fn($field) => str_starts_with($field, 'portfolio_'));

        $transactionData = $this->getTransactionFields($transactionFields, $this->outcomeFields['entry']);
        $portfolioData   = $this->getPortfolioFields($portfolioFields, $this->outcomeFields['entry']);
        $formulaData = $this->getTradeReplayFormulas();

        $mergedData = [];
        foreach ($this->outcomeFields['entry'] as $field) {
            $portfolioItem = collect($portfolioData)->firstWhere('database_field', $field);
            if ($portfolioItem) {
                $mergedData[] = $this->applyIsEditable($portfolioItem, $formulaData);
                continue;
            }
            $transactionItem = collect($transactionData)->firstWhere('database_field', $field);
            if ($transactionItem) {
                $mergedData[] = $this->applyIsEditable($transactionItem, $formulaData);
            }
        }

        return $mergedData;
    }

    /**
     * Get Exit Outcome Fields (Merged Transaction & Portfolio Fields)
     *
     * @return array
     */
    public function getExitOutcomeFields(): array
    {
        $transactionFields = array_filter($this->outcomeFields['exit'], fn($field) => str_starts_with($field, 'transaction_'));
        $portfolioFields   = array_filter($this->outcomeFields['exit'], fn($field) => str_starts_with($field, 'portfolio_'));

        $transactionData = $this->getTransactionFields($transactionFields, $this->outcomeFields['exit']);
        $portfolioData   = $this->getPortfolioFields($portfolioFields, $this->outcomeFields['exit']);

        $formulaData = $this->getTradeReplayFormulas();

        $mergedData = [];
        foreach ($this->outcomeFields['exit'] as $field) {
            $portfolioItem = collect($portfolioData)->firstWhere('database_field', $field);
            if ($portfolioItem) {
                $mergedData[] = $this->applyIsEditable($portfolioItem, $formulaData);
                continue;
            }
            $transactionItem = collect($transactionData)->firstWhere('database_field', $field);
            if ($transactionItem) {
                $mergedData[] = $this->applyIsEditable($transactionItem, $formulaData);
            }
        }

        return $mergedData;
    }

    /**
     * Create a new trade as a draft and generate its first entry form.
     *
     * @return TradeBuilder
     */
    public function createTradeWithEntry(): TradeBuilder
    {
        $user = Auth::user();

        $trade = TradeBuilder::create([
            'user_id' => $user->id,
            'title'   => TradeBuilder::getNextDraftTitle(),
            'status'  => 'draft',
        ]);

        TradeBuilderForm::create([
            'trade_builder_id' => $trade->id,
            'form_type'        => 'entry',
            'form_fields'      => array_fill_keys($this->entryFields, ''),
        ]);

        return $trade;
    }
}
