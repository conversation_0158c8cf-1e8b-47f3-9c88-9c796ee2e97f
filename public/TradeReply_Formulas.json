[{"DATABASE FIELD": "id", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_id", "FORMULA": {"usr": 0, "api": 0, "sys": 1, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_id", "FORMULA": {"usr": 0, "api": 0, "sys": 1, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_id", "FORMULA": {"usr": 0, "api": 0, "sys": 1, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "datetime", "DATATYPE": "ISO 8601 format (YYYY-MM-DD HH:MM:SS ±HH:MM)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_datetime", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_datetime", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_datetime", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "date_hour", "DATATYPE": "YYYY-MM-DD HH:00", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_date_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "transaction_datetime", "format": "%Y-%m-%d %H:00"}}}, "TRADE": {"DATABASE FIELD": "trade_date_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "trade_datetime", "format": "%Y-%m-%d %H:00"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_date_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "portfolio_datetime", "format": "%Y-%m-%d %H:00"}}}}}, {"DATABASE FIELD": "day", "DATATYPE": "Number (2-digit (e.g., 01-31))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_day", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "transaction_datetime", "format": "%d"}}}, "TRADE": {"DATABASE FIELD": "trade_day", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "trade_datetime", "format": "%d"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_day", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "portfolio_datetime", "format": "%d"}}}}}, {"DATABASE FIELD": "hour", "DATATYPE": "Number (2-digit (e.g., 00-23))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "transaction_datetime", "format": "%H"}}}, "TRADE": {"DATABASE FIELD": "trade_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "trade_datetime", "format": "%H"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "portfolio_datetime", "format": "%H"}}}}}, {"DATABASE FIELD": "month", "DATATYPE": "Number (2-digit (e.g., 01-12))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_month", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "transaction_datetime", "format": "%m"}}}, "TRADE": {"DATABASE FIELD": "trade_month", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "trade_datetime", "format": "%m"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_month", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATE_FORMAT", "field": "portfolio_datetime", "format": "%m"}}}}}, {"DATABASE FIELD": "nth_day", "DATATYPE": "Number (integer (e.g., 01))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_nth_day", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATEDIFF", "field1": "transaction_datetime", "field2": {"operation": "MIN", "field": "transaction_datetime", "window": "OVER ()"}}}}, "TRADE": {"DATABASE FIELD": "trade_nth_day", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATEDIFF", "field1": "trade_datetime", "field2": {"operation": "MIN", "field": "trade_datetime", "window": "OVER ()"}}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_nth_day", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DATEDIFF", "field1": "portfolio_datetime", "field2": {"operation": "MIN", "field": "portfolio_datetime", "window": "OVER ()"}}}}}}, {"DATABASE FIELD": "nth_hour", "DATATYPE": "Number (integer (e.g., 01))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_nth_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "hour", "field1": {"operation": "MIN", "field": "transaction_datetime", "window": "OVER ()"}, "field2": "transaction_datetime"}}}, "TRADE": {"DATABASE FIELD": "trade_nth_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "hour", "field1": {"operation": "MIN", "field": "trade_datetime", "window": "OVER ()"}, "field2": "trade_datetime"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_nth_hour", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "hour", "field1": {"operation": "MIN", "field": "portfolio_datetime", "window": "OVER ()"}, "field2": "portfolio_datetime"}}}}}, {"DATABASE FIELD": "nth_month", "DATATYPE": "Number (integer (e.g., 01))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_nth_month", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "month", "field1": {"operation": "MIN", "field": "transaction_datetime", "window": "OVER ()"}, "field2": "transaction_datetime"}}}, "TRADE": {"DATABASE FIELD": "trade_nth_month", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "month", "field1": {"operation": "MIN", "field": "trade_datetime", "window": "OVER ()"}, "field2": "trade_datetime"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_nth_month", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "month", "field1": {"operation": "MIN", "field": "portfolio_datetime", "window": "OVER ()"}, "field2": "portfolio_datetime"}}}}}, {"DATABASE FIELD": "nth_week", "DATATYPE": "Number (integer (e.g., 01))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_nth_week", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "week", "field1": {"operation": "MIN", "field": "transaction_datetime", "window": "OVER ()"}, "field2": "transaction_datetime"}}}, "TRADE": {"DATABASE FIELD": "trade_nth_week", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "week", "field1": {"operation": "MIN", "field": "trade_datetime", "window": "OVER ()"}, "field2": "trade_datetime"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_nth_week", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "week", "field1": {"operation": "MIN", "field": "portfolio_datetime", "window": "OVER ()"}, "field2": "portfolio_datetime"}}}}}, {"DATABASE FIELD": "nth_year", "DATATYPE": "Number (integer (e.g., 01))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_nth_year", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "year", "field1": {"operation": "MIN", "field": "transaction_datetime", "window": "OVER ()"}, "field2": "transaction_datetime"}}}, "TRADE": {"DATABASE FIELD": "trade_nth_year", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "year", "field1": {"operation": "MIN", "field": "trade_datetime", "window": "OVER ()"}, "field2": "trade_datetime"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_nth_year", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "TIMESTAMPDIFF", "unit": "year", "field1": {"operation": "MIN", "field": "portfolio_datetime", "window": "OVER ()"}, "field2": "portfolio_datetime"}}}}}, {"DATABASE FIELD": "week", "DATATYPE": "Number (2-digit (e.g., 01-53))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_week", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "WEEK", "field": "transaction_datetime", "mode": 1}}}, "TRADE": {"DATABASE FIELD": "trade_week", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "WEEK", "field": "trade_datetime", "mode": 1}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_week", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "WEEK", "field": "portfolio_datetime", "mode": 1}}}}}, {"DATABASE FIELD": "year", "DATATYPE": "Number (4-digit (e.g., 2024))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_year", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "YEAR", "field": "transaction_datetime"}}}, "TRADE": {"DATABASE FIELD": "trade_year", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "YEAR", "field": "trade_datetime"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_year", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "YEAR", "field": "portfolio_datetime"}}}}}, {"DATABASE FIELD": "timezone", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_timezone", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_timezone", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_timezone", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_initial_balance", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_initial_balance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_initial_balance"}}}, "TRADE": {"DATABASE FIELD": "trade_account_initial_balance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_initial_balance"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_initial_balance", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_initial_balance_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_initial_balance_type", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_initial_balance_type"}}}, "TRADE": {"DATABASE FIELD": "trade_account_initial_balance_type", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_initial_balance_type"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_initial_balance_type", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "manual_deposit", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_manual_deposit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_manual_deposit"}}}, "TRADE": {"DATABASE FIELD": "trade_manual_deposit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_manual_deposit"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_manual_deposit", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "manual_deposit_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_manual_deposit_type", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_manual_deposit_type"}}}, "TRADE": {"DATABASE FIELD": "trade_manual_deposit_type", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_manual_deposit_type"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_manual_deposit_type", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "max_risk_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_max_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_max_risk_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_max_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_max_risk_percentage"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_max_risk_percentage", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "max_risk_tolerance", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_max_risk_tolerance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_max_risk_tolerance"}}}, "TRADE": {"DATABASE FIELD": "trade_max_risk_tolerance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_max_risk_tolerance"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_max_risk_tolerance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "OR", "conditions": [{"operation": "==", "field": "portfolio_max_risk_percentage", "value": "0"}, {"operation": "IS_NULL", "field": "portfolio_max_risk_percentage"}]}, "true_case": "No Data", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "portfolio_max_risk_percentage", "value": "1"}, "true_case": "CONSERVATIVE", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "portfolio_max_risk_percentage", "value": "2"}, "true_case": "MODERATELY CONSERVATIVE", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "portfolio_max_risk_percentage", "value": "3"}, "true_case": "MODERATE", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "portfolio_max_risk_percentage", "value": "4"}, "true_case": "MODERATELY AGGRESSIVE", "false_case": "AGGRESSIVE"}}}}}}}}}, {"DATABASE FIELD": "profit_allocation_to_capital_reserve_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_profit_allocation_to_capital_reserve_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_profit_allocation_to_capital_reserve_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_profit_allocation_to_capital_reserve_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_profit_allocation_to_capital_reserve_percentage"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_profit_allocation_to_capital_reserve_percentage", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "capital_reserve", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_capital_reserve", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_capital_reserve"}}}, "TRADE": {"DATABASE FIELD": "trade_capital_reserve", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_capital_reserve"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_capital_reserve", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "portfolio_realized_pl_profit_loss", "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": ["portfolio_profit_allocation_to_capital_reserve_percentage", "portfolio_realized_pl_profit_loss"]}, "false_case": 0}}}}}, {"DATABASE FIELD": "trade_reserve", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_trade_reserve", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_trade_reserve"}}}, "TRADE": {"DATABASE FIELD": "trade_trade_reserve", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_trade_reserve"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_trade_reserve", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": ["portfolio_quantity_remaining", "portfolio_entry_price", "portfolio_leverage_factor"]}, "portfolio_margin_requirement_value"]}}}}}, {"DATABASE FIELD": "account_growth_goal", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_growth_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_goal"}}}, "TRADE": {"DATABASE FIELD": "trade_account_growth_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_goal"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_growth_goal", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": ["portfolio_account_size", "portfolio_account_growth_goal_factor"]}}}}}, {"DATABASE FIELD": "account_growth_goal_factor", "DATATYPE": "Text (Ratio (e.g., 0.00X))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_growth_goal_factor", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_goal_factor"}}}, "TRADE": {"DATABASE FIELD": "trade_account_growth_goal_factor", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_goal_factor"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_growth_goal_factor", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": ["portfolio_account_growth_goal", "portfolio_account_size"]}}}}}, {"DATABASE FIELD": "account_growth_value_of_goal", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_growth_value_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_value_of_goal"}}}, "TRADE": {"DATABASE FIELD": "trade_account_growth_value_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_value_of_goal"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_growth_value_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["portfolio_account_size", "portfolio_account_size_at_goal_creation"]}}}}}, {"DATABASE FIELD": "account_growth_percentage_of_goal", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_growth_percentage_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_percentage_of_goal"}}}, "TRADE": {"DATABASE FIELD": "trade_account_growth_percentage_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_percentage_of_goal"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_growth_percentage_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["portfolio_account_size", "portfolio_account_size_at_goal_creation"]}, {"operation": "SUBTRACT", "fields": ["portfolio_account_growth_goal", "portfolio_account_size_at_goal_creation"]}]}, 100]}}}}}, {"DATABASE FIELD": "account_growth_factor_of_goal", "DATATYPE": "Text (Ratio (e.g., 0.00X))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_growth_factor_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_factor_of_goal"}}}, "TRADE": {"DATABASE FIELD": "trade_account_growth_factor_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_growth_factor_of_goal"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_growth_factor_of_goal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": ["portfolio_account_size", "portfolio_account_growth_goal"]}}}}}, {"DATABASE FIELD": "account_fixed_stop_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_fixed_stop_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_fixed_stop_value"}}}, "TRADE": {"DATABASE FIELD": "trade_account_fixed_stop_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_fixed_stop_value"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_fixed_stop_value", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_fixed_stop_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_fixed_stop_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_fixed_stop_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_account_fixed_stop_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_fixed_stop_percentage"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_fixed_stop_percentage", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_stop_loss_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_stop_loss_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_loss_value"}}}, "TRADE": {"DATABASE FIELD": "trade_account_stop_loss_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_loss_value"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_stop_loss_value", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_stop_loss_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_stop_loss_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_loss_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_account_stop_loss_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_loss_percentage"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_stop_loss_percentage", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_stop_risk_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_stop_risk_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_risk_value"}}}, "TRADE": {"DATABASE FIELD": "trade_account_stop_risk_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_risk_value"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_stop_risk_value", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_stop_risk_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_stop_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_risk_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_account_stop_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_stop_risk_percentage"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_stop_risk_percentage", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "stock_unit_of_measurement", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stock_unit_of_measurement", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_stock_unit_of_measurement", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stock_unit_of_measurement", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "crypto_unit_of_measurement", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_crypto_unit_of_measurement", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_crypto_unit_of_measurement", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_crypto_unit_of_measurement", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "currency", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_currency", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_currency", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_currency", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "withdrawal", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_withdrawal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_withdrawal"}}}, "TRADE": {"DATABASE FIELD": "trade_withdrawal", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_withdrawal"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_withdrawal", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "account_size", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_size", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_size"}}}, "TRADE": {"DATABASE FIELD": "trade_account_size", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_size"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_size", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["portfolio_total_cash", "portfolio_market_value_of_open_positions", "portfolio_unrealized_pl_profit_loss"]}}}}}, {"DATABASE FIELD": "account_size_at_goal_creation", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_size_at_goal_creation", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_size_at_goal_creation"}}}, "TRADE": {"DATABASE FIELD": "trade_account_size_at_goal_creation", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_size_at_goal_creation"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_size_at_goal_creation", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "WHEN_SET", "condition": "portfolio_account_growth_goal", "value": "portfolio_account_size"}}}}}, {"DATABASE FIELD": "total_cash", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_total_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_total_cash"}}}, "TRADE": {"DATABASE FIELD": "trade_total_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_total_cash"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_total_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["portfolio_net_cash_changes", "portfolio_broker_fees"]}, "f2v": {"operation": "ADD", "fields": ["portfolio_allocated_cash", "portfolio_unallocated_cash"]}}}}}, {"DATABASE FIELD": "allocated_cash", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_allocated_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_allocated_cash"}}}, "TRADE": {"DATABASE FIELD": "trade_allocated_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_allocated_cash"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_allocated_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["portfolio_margin_requirement_value", "portfolio_pending_settlement_cash", "portfolio_broker_fees", "portfolio_margin_call_obligations"]}, "f2v": {"operation": "SUBTRACT", "fields": ["portfolio_total_cash", "portfolio_unallocated_cash"]}}}}}, {"DATABASE FIELD": "unallocated_cash", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_unallocated_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_unallocated_cash"}}}, "TRADE": {"DATABASE FIELD": "trade_unallocated_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_unallocated_cash"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_unallocated_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["portfolio_total_cash", "portfolio_allocated_cash"]}}}}}, {"DATABASE FIELD": "net_cash_changes", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_net_cash_changes", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_net_cash_changes"}}}, "TRADE": {"DATABASE FIELD": "trade_net_cash_changes", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_net_cash_changes"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_net_cash_changes", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": [{"operation": "ADD", "fields": ["portfolio_realized_pl_profit_loss", "portfolio_manual_deposit"]}, {"operation": "ADD", "fields": ["portfolio_withdrawal", "portfolio_broker_fees"]}]}}}}}, {"DATABASE FIELD": "cash_retention_ratio", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_cash_retention_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_cash_retention_ratio"}}}, "TRADE": {"DATABASE FIELD": "trade_cash_retention_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_cash_retention_ratio"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_cash_retention_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["portfolio_total_cash", "portfolio_account_initial_balance"]}, 100]}}}}}, {"DATABASE FIELD": "account_available", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_available", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_available"}}}, "TRADE": {"DATABASE FIELD": "trade_account_available", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_account_available"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_available", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["portfolio_total_cash", {"operation": "SUBTRACT", "fields": ["portfolio_allocated_cash", "portfolio_broker_fees"]}]}}}}}, {"DATABASE FIELD": "margin_requirement_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_margin_requirement_value", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_margin_requirement_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_margin_requirement_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_margin_requirement_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_margin_requirement_value"]}}}}}, {"DATABASE FIELD": "margin_requirement_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_margin_requirement_percentage", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_margin_requirement_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_margin_requirement_percentage", "transaction_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_margin_requirement_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_margin_requirement_percentage", "trade_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["trade_quantity_purchased"]}]}}}}}, {"DATABASE FIELD": "account_utilization_rate", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_utilization_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_market_value_of_open_positions", "transaction_account_size"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_account_utilization_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_account_utilization_rate", "transaction_market_value_of_open_positions"]}]}, {"operation": "SUM", "fields": ["transaction_market_value_of_open_positions"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_utilization_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_account_utilization_rate", "trade_market_value_of_open_positions"]}]}, {"operation": "SUM", "fields": ["trade_market_value_of_open_positions"]}]}}}}}, {"DATABASE FIELD": "account_commitment_rate", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_account_commitment_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "ADD", "fields": ["transaction_market_value_of_open_positions", "transaction_allocated_cash"]}, "transaction_account_size"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_account_commitment_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_account_commitment_rate", "transaction_account_size"]}]}, {"operation": "SUM", "fields": ["transaction_account_size"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_account_commitment_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_account_commitment_rate", "trade_account_size"]}]}, {"operation": "SUM", "fields": ["trade_account_size"]}]}}}}}, {"DATABASE FIELD": "net_liquidation_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_net_liquidation_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": [{"operation": "ADD", "fields": ["transaction_total_cash", "transaction_market_value_of_open_positions", "transaction_unrealized_pl_profit_loss"]}, "transaction_liabilities"]}}}, "TRADE": {"DATABASE FIELD": "trade_net_liquidation_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_net_liquidation_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_net_liquidation_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_net_liquidation_value"]}}}}}, {"DATABASE FIELD": "liabilities", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_liabilities", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["transaction_borrowed_amounts_for_trade", "transaction_interest", "transaction_short_sale_proceeds", "transaction_broker_fees"]}}}, "TRADE": {"DATABASE FIELD": "trade_liabilities", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_liabilities"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_liabilities", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_liabilities"]}}}}}, {"DATABASE FIELD": "borrowed_amounts_for_trade", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_borrowed_amounts_for_trade", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["transaction_margin_debt", "transaction_margin_call_obligations"]}}}, "TRADE": {"DATABASE FIELD": "trade_borrowed_amounts_for_trade", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_borrowed_amounts_for_trade"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_borrowed_amounts_for_trade", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_borrowed_amounts_for_trade"]}}}}}, {"DATABASE FIELD": "margin_debt", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_margin_debt", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_margin_debt", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_margin_debt"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_margin_debt", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_margin_debt"]}}}}}, {"DATABASE FIELD": "interest", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_interest", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": ["transaction_margin_debt", "transaction_annual_percentage_rate_apr", {"operation": "DIVIDE", "fields": ["transaction_days_borrowed", 365]}]}}}, "TRADE": {"DATABASE FIELD": "trade_interest", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_interest"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_interest", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_interest"]}}}}}, {"DATABASE FIELD": "annual_percentage_rate_apr", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_annual_percentage_rate_apr", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_annual_percentage_rate_apr", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_annual_percentage_rate_apr", "transaction_margin_debt"]}]}, {"operation": "SUM", "fields": ["transaction_margin_debt"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_annual_percentage_rate_apr", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_annual_percentage_rate_apr", "trade_margin_debt"]}]}, {"operation": "SUM", "fields": ["trade_margin_debt"]}]}}}}}, {"DATABASE FIELD": "days_borrowed", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_days_borrowed", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_days_borrowed", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_days_borrowed"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_days_borrowed", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_days_borrowed"]}}}}}, {"DATABASE FIELD": "short_sale_proceeds", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_short_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "SHORT"}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_closed", "transaction_exit_price"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_short_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_short_sale_proceeds"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_short_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_short_sale_proceeds"]}}}}}, {"DATABASE FIELD": "long_sale_proceeds", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_long_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_closed", "transaction_exit_price"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_long_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_long_sale_proceeds"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_long_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_long_sale_proceeds"]}}}}}, {"DATABASE FIELD": "total_sale_proceeds", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_total_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["transaction_long_sale_proceeds", "transaction_short_sale_proceeds"]}}}, "TRADE": {"DATABASE FIELD": "trade_total_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_total_sale_proceeds"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_total_sale_proceeds", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_total_sale_proceeds"]}}}}}, {"DATABASE FIELD": "broker_fees", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_broker_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["transaction_commission_fees", "transaction_transaction_fees", "transaction_margin_fee_components"]}}}, "TRADE": {"DATABASE FIELD": "trade_broker_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_broker_fees"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_broker_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_broker_fees"]}}}}}, {"DATABASE FIELD": "commission_fees", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_commission_fees", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_commission_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_commission_fees"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_commission_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_commission_fees"]}}}}}, {"DATABASE FIELD": "transaction_fees", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_transaction_fees", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_transaction_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_transaction_fees"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_transaction_fees", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_transaction_fees"]}}}}}, {"DATABASE FIELD": "margin_fee_components", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_margin_fee_components", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_margin_fee_components", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_margin_fee_components"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_margin_fee_components", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_margin_fee_components"]}}}}}, {"DATABASE FIELD": "margin_call_obligations", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_margin_call_obligations", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_margin_call_obligations", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_margin_call_obligations"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_margin_call_obligations", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_margin_call_obligations"]}}}}}, {"DATABASE FIELD": "market_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_market_price", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_market_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_market_price", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_market_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_market_price", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "market_value_of_open_positions", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_market_value_of_open_positions", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": ["transaction_market_price", "transaction_quantity_remaining"]}}}, "TRADE": {"DATABASE FIELD": "trade_market_value_of_open_positions", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_market_value_of_open_positions"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_market_value_of_open_positions", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_market_value_of_open_positions"]}}}}}, {"DATABASE FIELD": "pending_settlement_cash", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_pending_settlement_cash", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_pending_settlement_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_pending_settlement_cash"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_pending_settlement_cash", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_pending_settlement_cash"]}}}}}, {"DATABASE FIELD": "total_credit_line", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_total_credit_line", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_total_credit_line"}}}, "TRADE": {"DATABASE FIELD": "trade_total_credit_line", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_total_credit_line"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_total_credit_line", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "credit_line_availability", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_credit_line_availability", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_credit_line_availability"}}}, "TRADE": {"DATABASE FIELD": "trade_credit_line_availability", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_credit_line_availability"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_credit_line_availability", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["portfolio_total_credit_line", "portfolio_used_credit_line"]}}}}}, {"DATABASE FIELD": "used_credit_line", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_used_credit_line", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_used_credit_line"}}}, "TRADE": {"DATABASE FIELD": "trade_used_credit_line", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "portfolio_used_credit_line"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_used_credit_line", "FORMULA": {"usr": 0, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["portfolio_total_credit_line", "portfolio_credit_line_availability"]}}}}}, {"DATABASE FIELD": "risk_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}]}, "transaction_quantity_remaining"]}}}, "TRADE": {"DATABASE FIELD": "trade_risk_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_risk_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_risk_value"]}}}}}, {"DATABASE FIELD": "risk_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}]}, "transaction_quantity_remaining"]}, "transaction_account_size"]}, 100]}, "f2v": {"operation": "REFERENCE", "field": "portfolio_max_risk_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_risk_percentage", "transaction_account_size"]}]}, {"operation": "SUM", "fields": ["transaction_account_size"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_risk_percentage", "trade_account_size"]}]}, {"operation": "SUM", "fields": ["trade_account_size"]}]}}}}}, {"DATABASE FIELD": "risk_tolerance", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_tolerance", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "OR", "conditions": [{"operation": "==", "field": "transaction_risk_percentage", "value": "0"}, {"operation": "IS_NULL", "field": "transaction_risk_percentage"}]}, "true_case": "No Data", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "transaction_risk_percentage", "value": "1"}, "true_case": "CONSERVATIVE", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "transaction_risk_percentage", "value": "2"}, "true_case": "MODERATELY CONSERVATIVE", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "transaction_risk_percentage", "value": "3"}, "true_case": "MODERATE", "false_case": {"operation": "IF", "condition": {"operation": "<=", "field": "transaction_risk_percentage", "value": "4"}, "true_case": "MODERATELY AGGRESSIVE", "false_case": "AGGRESSIVE"}}}}}, "f2v": {"operation": "REFERENCE", "field": "portfolio_max_risk_tolerance"}}}, "TRADE": {"DATABASE FIELD": "trade_risk_tolerance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MODE", "fields": ["transaction_risk_tolerance"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_tolerance", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MODE", "fields": ["trade_risk_tolerance"]}}}}}, {"DATABASE FIELD": "risk_per_share_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_per_share_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}]}}}, "TRADE": {"DATABASE FIELD": "trade_risk_per_share_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_risk_per_share_value", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_per_share_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_risk_per_share_value", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "risk_per_share_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_per_share_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}]}, "transaction_entry_price"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_risk_per_share_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_risk_per_share_percentage", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_per_share_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_risk_per_share_percentage", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "win_rate", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_win_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": 1, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_win_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": ["transaction_win_rate"]}, {"operation": "COUNT", "fields": ["transaction_win_rate"]}]}, 100]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_win_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": ["trade_win_rate"]}, {"operation": "COUNT", "fields": ["trade_win_rate"]}]}, 100]}}}}}, {"DATABASE FIELD": "realized_pl_profit_loss", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_realized_pl_profit_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_exit_price", "transaction_entry_price"]}, "transaction_quantity_closed"]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_exit_price"]}, "transaction_quantity_closed"]}}}}, "TRADE": {"DATABASE FIELD": "trade_realized_pl_profit_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_realized_pl_profit_loss"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_realized_pl_profit_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_realized_pl_profit_loss"]}}}}}, {"DATABASE FIELD": "unrealized_pl_profit_loss", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_unrealized_pl_profit_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_market_price", "transaction_entry_price"]}, "transaction_quantity_remaining"]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_market_price"]}, "transaction_quantity_remaining"]}}}}, "TRADE": {"DATABASE FIELD": "trade_unrealized_pl_profit_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_unrealized_pl_profit_loss"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_unrealized_pl_profit_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_unrealized_pl_profit_loss"]}}}}}, {"DATABASE FIELD": "status", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_status", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_quantity_remaining", "value": 0}, "true_case": "OPEN", "false_case": "CLOSED"}}}, "TRADE": {"DATABASE FIELD": "trade_status", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}, "value": 0}, "true_case": "OPEN", "false_case": "CLOSED"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_status", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["trade_quantity_remaining"]}, "value": 0}, "true_case": "OPEN", "false_case": "CLOSED"}}}}}, {"DATABASE FIELD": "quantity_purchased", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_quantity_purchased", "FORMULA": {"usr": 1, "api": 0, "sys": 0, "f1": 1, "f2": 1, "f3": 1, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_risk_value", "transaction_leverage_factor"]}, "transaction_risk_per_share_value"]}, "f2v": {"operation": "DIVIDE", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_investment_value", "transaction_leverage_factor"]}, "transaction_entry_price"]}, "f3v": {"operation": "DIVIDE", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_account_size", {"operation": "DIVIDE", "fields": ["transaction_risk_percentage", 100]}]}, "transaction_leverage_factor"]}, "transaction_risk_per_share_value"]}}}, "TRADE": {"DATABASE FIELD": "trade_quantity_purchased", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_quantity_purchased", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_quantity_purchased"]}}}}}, {"DATABASE FIELD": "quantity_remaining", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_quantity_remaining", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["transaction_quantity_purchased", "transaction_quantity_closed"]}}}, "TRADE": {"DATABASE FIELD": "trade_quantity_remaining", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_quantity_remaining", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_quantity_remaining"]}}}}}, {"DATABASE FIELD": "quantity_closed", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_quantity_closed", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["transaction_quantity_purchased", "transaction_quantity_remaining"]}}}, "TRADE": {"DATABASE FIELD": "trade_quantity_closed", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_quantity_closed"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_quantity_closed", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_quantity_closed"]}}}}}, {"DATABASE FIELD": "total_quantity_traded", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_total_quantity_traded", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["transaction_quantity_purchased", "transaction_quantity_closed"]}}}, "TRADE": {"DATABASE FIELD": "trade_total_quantity_traded", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_total_quantity_traded"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_total_quantity_traded", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_total_quantity_traded"]}}}}}, {"DATABASE FIELD": "optimal_quantity", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_optimal_quantity", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_risk_per_share_value", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": ["transaction_risk_value", "transaction_risk_per_share_value"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_optimal_quantity", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_optimal_quantity"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_optimal_quantity", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_optimal_quantity"]}}}}}, {"DATABASE FIELD": "leverage_factor", "DATATYPE": "Text (Ratio (e.g., 2X, 5X))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_leverage_factor", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_leverage_factor", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_leverage_factor", {"operation": "DIVIDE", "fields": ["transaction_risk_value", "transaction_risk_per_share_value"]}]}]}, {"operation": "SUM", "fields": [{"operation": "DIVIDE", "fields": ["transaction_risk_value", "transaction_risk_per_share_value"]}]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_leverage_factor", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_leverage_factor", {"operation": "DIVIDE", "fields": ["trade_risk_value", "trade_risk_per_share_value"]}]}]}, {"operation": "SUM", "fields": [{"operation": "DIVIDE", "fields": ["trade_risk_value", "trade_risk_per_share_value"]}]}]}}}}}, {"DATABASE FIELD": "entry_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_entry_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_entry_price", "transaction_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_entry_price", "trade_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["trade_quantity_purchased"]}]}}}}}, {"DATABASE FIELD": "exit_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_exit_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": ["transaction_total_sale_proceeds", "transaction_quantity_closed"]}}}, "TRADE": {"DATABASE FIELD": "trade_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_exit_price", "transaction_quantity_closed"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_closed"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_exit_price", "trade_quantity_closed"]}]}, {"operation": "SUM", "fields": ["trade_quantity_closed"]}]}}}}}, {"DATABASE FIELD": "stop_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_risk_per_share_value"]}}}, "TRADE": {"DATABASE FIELD": "trade_stop_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_stop_price", "transaction_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_stop_price", "trade_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["trade_quantity_purchased"]}]}}}}}, {"DATABASE FIELD": "investment_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_investment_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": ["transaction_entry_price", "transaction_quantity_purchased"]}}}, "TRADE": {"DATABASE FIELD": "trade_investment_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_investment_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_investment_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_investment_value"]}}}}}, {"DATABASE FIELD": "investment_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_investment_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_investment_value", "transaction_account_size"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_investment_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_investment_percentage", "transaction_investment_value"]}]}, {"operation": "SUM", "fields": ["transaction_investment_value"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_investment_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_investment_percentage", "trade_investment_value"]}]}, {"operation": "SUM", "fields": ["trade_investment_value"]}]}}}}}, {"DATABASE FIELD": "allocated_profit", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_allocated_profit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_realized_pl_profit_loss", "transaction_profit_allocation_to_capital_reserve_percentage"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_allocated_profit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_allocated_profit"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_allocated_profit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_allocated_profit"]}}}}}, {"DATABASE FIELD": "asset_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_asset_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_asset_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_asset_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "entry_date", "DATATYPE": "Date (YYYY-MM-DD)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_entry_date", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_entry_date", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_entry_date", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "exit_date", "DATATYPE": "Date (YYYY-MM-DD)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_exit_date", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_exit_date", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_exit_date", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "entry_time", "DATATYPE": "Time (HH:MM:SS (24-hour format))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_entry_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_entry_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_entry_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "exit_time", "DATATYPE": "Time (HH:MM:SS (24-hour format))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_exit_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_exit_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_exit_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "ticker", "DATATYPE": "Alphanumeric", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_ticker", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_ticker", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_ticker", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "position_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_position_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_position_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_position_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "order_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_order_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_order_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_order_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "stop_loss_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_loss_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}, "transaction_quantity_remaining"]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_stop_price", "transaction_entry_price"]}, "transaction_quantity_remaining"]}}, "f2v": {"operation": "REFERENCE", "field": "portfolio_account_stop_loss_value"}}}, "TRADE": {"DATABASE FIELD": "trade_stop_loss_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_stop_loss_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_loss_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_stop_loss_value"]}}}}}, {"DATABASE FIELD": "stop_loss_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_loss_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}, "transaction_entry_price"]}, 100]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_stop_price", "transaction_entry_price"]}, "transaction_entry_price"]}, 100]}}, "f2v": {"operation": "REFERENCE", "field": "portfolio_account_stop_loss_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_stop_loss_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_stop_loss_percentage"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_loss_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_stop_loss_percentage"]}}}}}, {"DATABASE FIELD": "fixed_stop_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_fixed_stop_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}]}, "f2v": {"operation": "REFERENCE", "field": "portfolio_account_fixed_stop_value"}}}, "TRADE": {"DATABASE FIELD": "trade_fixed_stop_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_fixed_stop_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_fixed_stop_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_fixed_stop_value"]}}}}}, {"DATABASE FIELD": "fixed_stop_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_fixed_stop_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_stop_price"]}]}, "transaction_entry_price"]}, 100]}, "f2v": {"operation": "REFERENCE", "field": "portfolio_account_fixed_stop_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_fixed_stop_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_fixed_stop_percentage"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_fixed_stop_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_fixed_stop_percentage"]}}}}}, {"DATABASE FIELD": "stop_risk_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_risk_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["transaction_stop_loss_value", {"operation": "MULTIPLY", "fields": ["transaction_account_available", {"operation": "DIVIDE", "fields": ["transaction_max_risk_percentage", 100]}]}]}, "f2v": {"operation": "REFERENCE", "field": "portfolio_account_stop_risk_value"}}}, "TRADE": {"DATABASE FIELD": "trade_stop_risk_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_stop_risk_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_risk_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_stop_risk_value"]}}}}}, {"DATABASE FIELD": "stop_risk_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_risk_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 1, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_stop_risk_value", "transaction_account_available"]}, 100]}, "f2v": {"operation": "REFERENCE", "field": "portfolio_account_stop_risk_percentage"}}}, "TRADE": {"DATABASE FIELD": "trade_stop_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_stop_risk_percentage"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_risk_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_stop_risk_percentage"]}}}}}, {"DATABASE FIELD": "stop_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_stop_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "trailing_stop_hit", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_trailing_stop_hit", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_trailing_stop_hit", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_trailing_stop_hit", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "distance_type", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_distance_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_distance_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_distance_type", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "distance_to_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_distance_to_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_distance_to_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_distance_to_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "distance_to_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_distance_to_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_distance_to_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_distance_to_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "activation_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_activation_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_activation_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_activation_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "snapshot", "DATATYPE": "Visual (Image/Graph) PNG,JPEG/JPG", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_snapshot", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_snapshot", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_snapshot", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "comments", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_comments", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_comments", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_comments", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "outcome", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_outcome", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_quantity_remaining", "value": 0}, "true_case": "OPEN", "false_case": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "WIN", "false_case": "LOSS"}}}}, "TRADE": {"DATABASE FIELD": "trade_outcome", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "COUNTIF", "field": "transaction_outcome", "value": "OPEN"}, "value": 0}, "true_case": "OPEN", "false_case": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUMIF", "field": "transaction_outcome", "value": "WIN", "sum_field": "transaction_realized_pl_profit_loss"}, "value": {"operation": "SUMIF", "field": "transaction_outcome", "value": "LOSS", "sum_field": "transaction_realized_pl_profit_loss"}}, "true_case": "WIN", "false_case": "LOSS"}}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_outcome", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUMIF", "field": "trade_outcome", "value": "WIN", "sum_field": "trade_realized_pl_profit_loss"}, "value": {"operation": "SUMIF", "field": "trade_outcome", "value": "LOSS", "sum_field": "trade_realized_pl_profit_loss"}}, "true_case": "WIN", "false_case": "LOSS"}}}}}, {"DATABASE FIELD": "wins", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_wins", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "=", "field": "transaction_outcome", "value": "WIN"}, "true_case": 1, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_wins", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "=", "field": "trade_outcome", "value": "WIN"}, "true_case": 1, "false_case": 0}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_wins", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "=", "field": "portfolio_outcome", "value": "WIN"}, "true_case": 1, "false_case": 0}}}}}, {"DATABASE FIELD": "losses", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_losses", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "=", "field": "transaction_outcome", "value": "LOSS"}, "true_case": 1, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_losses", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "=", "field": "trade_outcome", "value": "LOSS"}, "true_case": 1, "false_case": 0}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_losses", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "=", "field": "portfolio_outcome", "value": "LOSS"}, "true_case": 1, "false_case": 0}}}}}, {"DATABASE FIELD": "total_wins_and_losses", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_total_wins_and_losses", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["transaction_wins", "transaction_losses"]}}}, "TRADE": {"DATABASE FIELD": "trade_total_wins_and_losses", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["trade_wins", "trade_losses"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_total_wins_and_losses", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "ADD", "fields": ["portfolio_wins", "portfolio_losses"]}}}}}, {"DATABASE FIELD": "stop_violation_alert", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_stop_violation_alert", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_stop_violation_alert", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "COUNTIF", "field": "transaction_stop_violation_alert", "value": "YES", "operator": ">", "threshold": 0}, "true_case": "YES", "false_case": "NO"}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_stop_violation_alert", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "COUNTIF", "field": "trade_stop_violation_alert", "value": "YES", "operator": ">", "threshold": 0}, "true_case": "YES", "false_case": "NO"}}}}}, {"DATABASE FIELD": "expiration_time", "DATATYPE": "ISO 8601 Date-Time (YYYY-MM-DDTHH:mm:ssZ)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_expiration_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_expiration_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_expiration_time", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "expiration_variable", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_expiration_variable", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_expiration_variable", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_expiration_variable", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "movement_status", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_movement_status", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_movement_status", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_movement_status", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "movement_variable", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_movement_variable", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_movement_variable", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_movement_variable", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "spread", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_spread", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["transaction_ask_price", "transaction_bid_price"]}}}, "TRADE": {"DATABASE FIELD": "trade_spread", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_spread"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_spread", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_spread"]}}}}}, {"DATABASE FIELD": "ask_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_ask_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_ask_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_ask_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "bid_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_bid_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_bid_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_bid_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "market_depth", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_market_depth", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_market_depth", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_market_depth", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "risk_adjusted_return", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_adjusted_return", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_risk_value", "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_realized_pl_profit_loss", "transaction_risk_value"]}, 100]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_risk_adjusted_return", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["transaction_risk_value"]}, "value": 0}, "true_case": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_risk_adjusted_return", "transaction_risk_value"]}]}, {"operation": "SUM", "fields": ["transaction_risk_value"]}]}, "false_case": 0}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_adjusted_return", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["trade_risk_value"]}, "value": 0}, "true_case": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_risk_adjusted_return", "trade_risk_value"]}]}, {"operation": "SUM", "fields": ["trade_risk_value"]}]}, "false_case": 0}}}}}, {"DATABASE FIELD": "return_on_investment_roi", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_return_on_investment_roi", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_investment_value", "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_realized_pl_profit_loss", "transaction_investment_value"]}, 100]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_return_on_investment_roi", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["transaction_investment_value"]}, "value": 0}, "true_case": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_return_on_investment_roi", "transaction_investment_value"]}]}, {"operation": "SUM", "fields": ["transaction_investment_value"]}]}, "false_case": 0}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_return_on_investment_roi", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["trade_investment_value"]}, "value": 0}, "true_case": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_return_on_investment_roi", "trade_investment_value"]}]}, {"operation": "SUM", "fields": ["trade_investment_value"]}]}, "false_case": 0}}}}}, {"DATABASE FIELD": "profit_consistency", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_profit_consistency", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": 1, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_profit_consistency", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}, "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_profit_consistency", "transaction_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}]}, 100]}, "false_case": 0}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_profit_consistency", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "SUM", "fields": ["trade_quantity_purchased"]}, "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_profit_consistency", "trade_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["trade_quantity_purchased"]}]}, 100]}, "false_case": 0}}}}}, {"DATABASE FIELD": "target_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_target_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "ADD", "fields": ["transaction_entry_price", {"operation": "DIVIDE", "fields": ["transaction_desired_profit", "transaction_quantity_remaining"]}]}, "false_case": {"operation": "SUBTRACT", "fields": ["transaction_entry_price", {"operation": "DIVIDE", "fields": ["transaction_desired_profit", "transaction_quantity_remaining"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_target_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_target_price", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_target_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_target_price", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "desired_profit", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_desired_profit", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_entry_price"]}]}, "false_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_target_price"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_desired_profit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_desired_profit", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_desired_profit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_desired_profit", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "profit_factor", "DATATYPE": "Number (Ratio)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_profit_factor", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}, {"operation": "IF", "condition": {"operation": "<", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": {"operation": "ABS", "fields": ["transaction_realized_pl_profit_loss"]}, "false_case": 0}]}}}, "TRADE": {"DATABASE FIELD": "trade_profit_factor", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_profit_factor", "transaction_realized_pl_profit_loss"]}]}, {"operation": "SUM", "fields": ["transaction_realized_pl_profit_loss"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_profit_factor", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_profit_factor", "trade_realized_pl_profit_loss"]}]}, {"operation": "SUM", "fields": ["trade_realized_pl_profit_loss"]}]}}}}}, {"DATABASE FIELD": "reward_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_reward_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_entry_price"]}]}, "false_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_target_price"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_reward_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_reward_value", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_reward_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_reward_value", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "reward_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_reward_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_entry_price"]}, "transaction_entry_price"]}, 100]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_target_price"]}, "transaction_entry_price"]}, 100]}}}}, "TRADE": {"DATABASE FIELD": "trade_reward_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_reward_percentage", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_reward_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_reward_percentage", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "risk_reward_ratio", "DATATYPE": "Text (Ratio (e.g., 0.00X))", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_reward_ratio", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_reward_value", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": ["transaction_risk_value", "transaction_reward_value"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_risk_reward_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_risk_reward_ratio", "transaction_risk_value"]}]}, {"operation": "SUM", "fields": ["transaction_risk_value"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_reward_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_risk_reward_ratio", "trade_risk_value"]}]}, {"operation": "SUM", "fields": ["trade_risk_value"]}]}}}}}, {"DATABASE FIELD": "entry_signal", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_entry_signal", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_entry_signal", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_entry_signal", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "exit_signal", "DATATYPE": "Text", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_exit_signal", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_exit_signal", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_exit_signal", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}}}, {"DATABASE FIELD": "take_profit_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_take_profit_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "ADD", "fields": ["transaction_entry_price", {"operation": "DIVIDE", "fields": ["transaction_desired_profit", "transaction_quantity_remaining"]}]}, "false_case": {"operation": "SUBTRACT", "fields": ["transaction_entry_price", {"operation": "DIVIDE", "fields": ["transaction_desired_profit", "transaction_quantity_remaining"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_take_profit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_take_profit_price", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_take_profit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_take_profit_price", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "take_profit_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_take_profit_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["take_profit_price", "transaction_entry_price"]}, "transaction_quantity_remaining"]}}}, "TRADE": {"DATABASE FIELD": "trade_take_profit_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_take_profit_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_take_profit_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_take_profit_value"]}}}}}, {"DATABASE FIELD": "take_profit_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_take_profit_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["take_profit_price", "transaction_entry_price"]}, "transaction_entry_price"]}, 100]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "take_profit_price"]}, "transaction_entry_price"]}, 100]}}}}, "TRADE": {"DATABASE FIELD": "trade_take_profit_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_take_profit_percentage", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_take_profit_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_take_profit_percentage", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "full_profit_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_full_profit_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_entry_price"]}]}, "false_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_target_price"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_full_profit_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_full_profit_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_full_profit_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_full_profit_value"]}}}}}, {"DATABASE FIELD": "full_profit_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_full_profit_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_entry_price"]}, "transaction_entry_price"]}, 100]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_entry_price", "transaction_target_price"]}, "transaction_entry_price"]}, 100]}}}}, "TRADE": {"DATABASE FIELD": "trade_full_profit_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_full_profit_percentage", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_full_profit_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_full_profit_percentage", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "full_profit_missed_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_full_profit_missed_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_exit_price"]}]}, "false_case": {"operation": "MULTIPLY", "fields": ["transaction_quantity_remaining", {"operation": "SUBTRACT", "fields": ["transaction_exit_price", "transaction_target_price"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_full_profit_missed_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_full_profit_missed_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_full_profit_missed_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_full_profit_missed_value"]}}}}}, {"DATABASE FIELD": "full_profit_missed_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_full_profit_missed_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"field": "transaction_position_type", "operator": "=", "value": "LONG"}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_target_price", "transaction_exit_price"]}, "transaction_target_price"]}, 100]}, "false_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_exit_price", "transaction_target_price"]}, "transaction_target_price"]}, 100]}}}}, "TRADE": {"DATABASE FIELD": "trade_full_profit_missed_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_full_profit_missed_percentage", "transaction_realized_pl_profit_loss"]}]}, {"operation": "SUM", "fields": ["transaction_realized_pl_profit_loss"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_full_profit_missed_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_full_profit_missed_percentage", "trade_realized_pl_profit_loss"]}]}, {"operation": "SUM", "fields": ["trade_realized_pl_profit_loss"]}]}}}}}, {"DATABASE FIELD": "full_profit_efficiency_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_full_profit_efficiency_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_realized_pl_profit_loss"}}}, "TRADE": {"DATABASE FIELD": "trade_full_profit_efficiency_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_full_profit_efficiency_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_full_profit_efficiency_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_full_profit_efficiency_value"]}}}}}, {"DATABASE FIELD": "full_profit_efficiency_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_full_profit_efficiency_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "full_profit_value", "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_realized_pl_profit_loss", "full_profit_value"]}, 100]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_full_profit_efficiency_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_full_profit_efficiency_percentage", {"operation": "ABS", "fields": ["transaction_realized_pl_profit_loss"]}]}]}, {"operation": "SUM", "fields": [{"operation": "ABS", "fields": ["transaction_realized_pl_profit_loss"]}]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_full_profit_efficiency_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_full_profit_efficiency_percentage", {"operation": "ABS", "fields": ["trade_realized_pl_profit_loss"]}]}]}, {"operation": "SUM", "fields": [{"operation": "ABS", "fields": ["trade_realized_pl_profit_loss"]}]}]}}}}}, {"DATABASE FIELD": "deviation_profit_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_deviation_profit_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["transaction_realized_pl_profit_loss", "transaction_desired_profit"]}}}, "TRADE": {"DATABASE FIELD": "trade_deviation_profit_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_deviation_profit_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_deviation_profit_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_deviation_profit_value"]}}}}}, {"DATABASE FIELD": "deviation_profit_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_deviation_profit_percentage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_realized_pl_profit_loss", "transaction_desired_profit"]}, "transaction_desired_profit"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_deviation_profit_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_deviation_profit_percentage", "transaction_desired_profit"]}]}, {"operation": "SUM", "fields": ["transaction_desired_profit"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_deviation_profit_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_deviation_profit_percentage", "trade_desired_profit"]}]}, {"operation": "SUM", "fields": ["trade_desired_profit"]}]}}}}}, {"DATABASE FIELD": "duration_in_days", "DATATYPE": "Number (Duration)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_duration_in_days", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "IS NULL", "field": "transaction_exit_date"}, "true_case": {"operation": "SUBTRACT", "fields": ["TODAY()", "transaction_entry_date"]}, "false_case": {"operation": "SUBTRACT", "fields": ["transaction_exit_date", "transaction_entry_date"]}}}}, "TRADE": {"DATABASE FIELD": "trade_duration_in_days", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "IS NULL", "field": "trade_exit_date"}, "true_case": {"operation": "SUBTRACT", "fields": ["TODAY()", "trade_entry_date"]}, "false_case": {"operation": "SUBTRACT", "fields": ["trade_exit_date", "trade_entry_date"]}}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_duration_in_days", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "IS NULL", "field": "portfolio_exit_date"}, "true_case": {"operation": "SUBTRACT", "fields": ["TODAY()", "portfolio_entry_date"]}, "false_case": {"operation": "SUBTRACT", "fields": ["portfolio_exit_date", "portfolio_entry_date"]}}}}}}, {"DATABASE FIELD": "duration_in_hours", "DATATYPE": "Number (Duration)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_duration_in_hours", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "IS NULL", "field": "transaction_exit_date"}, "true_case": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["TODAY()", "transaction_entry_date"]}, 24]}, {"operation": "SUBTRACT", "fields": ["NOW()", "transaction_entry_time"]}]}, "false_case": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_exit_date", "transaction_entry_date"]}, 24]}, {"operation": "SUBTRACT", "fields": ["transaction_exit_time", "transaction_entry_time"]}]}}}}, "TRADE": {"DATABASE FIELD": "trade_duration_in_hours", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "IS NULL", "field": "trade_exit_date"}, "true_case": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["TODAY()", "trade_entry_date"]}, 24]}, {"operation": "SUBTRACT", "fields": ["NOW()", "trade_entry_time"]}]}, "false_case": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["trade_exit_date", "trade_entry_date"]}, 24]}, {"operation": "SUBTRACT", "fields": ["trade_exit_time", "trade_entry_time"]}]}}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_duration_in_hours", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "IS NULL", "field": "portfolio_exit_date"}, "true_case": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["TODAY()", "portfolio_entry_date"]}, 24]}, {"operation": "SUBTRACT", "fields": ["NOW()", "portfolio_entry_time"]}]}, "false_case": {"operation": "ADD", "fields": [{"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["portfolio_exit_date", "portfolio_entry_date"]}, 24]}, {"operation": "SUBTRACT", "fields": ["portfolio_exit_time", "portfolio_entry_time"]}]}}}}}}, {"DATABASE FIELD": "maximum_drawdown_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_maximum_drawdown_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_peak_price", "transaction_trough_price"]}, "transaction_quantity_remaining"]}}}, "TRADE": {"DATABASE FIELD": "trade_maximum_drawdown_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_maximum_drawdown_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_maximum_drawdown_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_maximum_drawdown_value"]}}}}}, {"DATABASE FIELD": "maximum_drawdown_percentage", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_maximum_drawdown_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_peak_price", "transaction_trough_price"]}, "transaction_peak_price"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_maximum_drawdown_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_maximum_drawdown_percentage", "transaction_peak_price"]}]}, {"operation": "SUM", "fields": ["transaction_peak_price"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_maximum_drawdown_percentage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_maximum_drawdown_percentage", "trade_peak_price"]}]}, {"operation": "SUM", "fields": ["trade_peak_price"]}]}}}}}, {"DATABASE FIELD": "drawdown_duration", "DATATYPE": "Number (Duration)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_drawdown_duration", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_trough_price_datetime", "value": "transaction_peak_price_datetime"}, "true_case": {"operation": "DATEDIF", "fields": ["transaction_peak_price_datetime", "transaction_trough_price_datetime", "D"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_drawdown_duration", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_drawdown_duration"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_drawdown_duration", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_drawdown_duration"]}}}}}, {"DATABASE FIELD": "recovery_time_from_drawdown", "DATATYPE": "Number (Duration)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_recovery_time_from_drawdown", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_recovery_datetime", "value": "transaction_trough_price_datetime"}, "true_case": {"operation": "DATEDIF", "fields": ["transaction_trough_price_datetime", "transaction_recovery_datetime", "D"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_recovery_time_from_drawdown", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_recovery_time_from_drawdown"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_recovery_time_from_drawdown", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_recovery_time_from_drawdown"]}}}}}, {"DATABASE FIELD": "recovery_datetime", "DATATYPE": "ISO 8601 format (YYYY-MM-DD HH:MM:SS ±HH:MM)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_recovery_datetime", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_recovery_datetime", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_recovery_datetime"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_recovery_datetime", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_recovery_datetime"]}}}}}, {"DATABASE FIELD": "peak_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_peak_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_peak_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["transaction_peak_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_peak_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["trade_peak_price"]}}}}}, {"DATABASE FIELD": "trough_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_trough_price", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_trough_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["transaction_trough_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_trough_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["trade_trough_price"]}}}}}, {"DATABASE FIELD": "peak_price_datetime", "DATATYPE": "ISO 8601 format (YYYY-MM-DD HH:MM:SS ±HH:MM)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_peak_price_datetime", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_peak_price_datetime", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_peak_price_datetime"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_peak_price_datetime", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_peak_price_datetime"]}}}}}, {"DATABASE FIELD": "trough_price_datetime", "DATATYPE": "ISO 8601 format (YYYY-MM-DD HH:MM:SS ±HH:MM)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_trough_price_datetime", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_trough_price_datetime", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_trough_price_datetime"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_trough_price_datetime", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_trough_price_datetime"]}}}}}, {"DATABASE FIELD": "slippage", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_slippage", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_slippage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["transaction_slippage"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_slippage", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUM", "fields": ["trade_slippage"]}}}}}, {"DATABASE FIELD": "risk_free_rate", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_risk_free_rate", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_risk_free_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_risk_free_rate"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_risk_free_rate", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_risk_free_rate"]}}}}}, {"DATABASE FIELD": "volatility", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_volatility", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": [{"operation": "ABS", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_daily_return", {"operation": "AVERAGE", "fields": ["transaction_daily_return"]}]}]}]}}}, "TRADE": {"DATABASE FIELD": "trade_volatility", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_volatility", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_volatility", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_volatility", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "daily_return", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_daily_return", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_close_price_today", "transaction_close_price_yesterday"]}, "transaction_close_price_yesterday"]}, 100]}}}, "TRADE": {"DATABASE FIELD": "trade_daily_return", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_daily_return", "transaction_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_purchased"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_daily_return", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_daily_return", "trade_quantity_purchased"]}]}, {"operation": "SUM", "fields": ["trade_quantity_purchased"]}]}}}}}, {"DATABASE FIELD": "close_price_today", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_close_price_today", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_close_price_today", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_close_price_today", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_close_price_today", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_close_price_today", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "close_price_yesterday", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_close_price_yesterday", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_close_price_yesterday", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_close_price_yesterday", "transaction_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["transaction_quantity_remaining"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_close_price_yesterday", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_close_price_yesterday", "trade_quantity_remaining"]}]}, {"operation": "SUM", "fields": ["trade_quantity_remaining"]}]}}}}}, {"DATABASE FIELD": "downside_deviation", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_downside_deviation", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": {"operation": "COUNTIF", "range": "transaction_daily_return", "condition": {"operation": "<", "field": "transaction_risk_free_rate"}}, "value": 0}, "true_case": {"operation": "SQRT", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "IF", "condition": {"operation": "<", "field": "transaction_daily_return", "value": "transaction_risk_free_rate"}, "true_case": {"operation": "POWER", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_daily_return", "transaction_risk_free_rate"]}, 2]}, "false_case": 0}]}, {"operation": "COUNTIF", "range": "transaction_daily_return", "condition": {"operation": "<", "field": "transaction_risk_free_rate"}}]}]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_downside_deviation", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SQRT", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "POWER", "fields": ["transaction_downside_deviation", 2]}]}, {"operation": "COUNT", "fields": ["transaction_downside_deviation"]}]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_downside_deviation", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SQRT", "fields": [{"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "POWER", "fields": ["trade_downside_deviation", 2]}]}, {"operation": "COUNT", "fields": ["trade_downside_deviation"]}]}]}}}}}, {"DATABASE FIELD": "sharpe_ratio", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_sharpe_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_volatility", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_return_on_investment_roi", "transaction_risk_free_rate"]}, "transaction_volatility"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_sharpe_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_sharpe_ratio", "transaction_return_on_investment_roi"]}]}, {"operation": "SUM", "fields": ["transaction_return_on_investment_roi"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_sharpe_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_sharpe_ratio", "trade_return_on_investment_roi"]}]}, {"operation": "SUM", "fields": ["trade_return_on_investment_roi"]}]}}}}}, {"DATABASE FIELD": "sortino_ratio", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_sortino_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_downside_deviation", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": [{"operation": "SUBTRACT", "fields": ["transaction_return_on_investment_roi", "transaction_risk_free_rate"]}, "transaction_downside_deviation"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_sortino_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_sortino_ratio", "transaction_return_on_investment_roi"]}]}, {"operation": "SUM", "fields": ["transaction_return_on_investment_roi"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_sortino_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_sortino_ratio", "trade_return_on_investment_roi"]}]}, {"operation": "SUM", "fields": ["trade_return_on_investment_roi"]}]}}}}}, {"DATABASE FIELD": "calmar_ratio", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_calmar_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_maximum_drawdown_percentage", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": ["transaction_return_on_investment_roi", "transaction_maximum_drawdown_percentage"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_calmar_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_calmar_ratio", "transaction_return_on_investment_roi"]}]}, {"operation": "SUM", "fields": ["transaction_return_on_investment_roi"]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_calmar_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_calmar_ratio", "trade_return_on_investment_roi"]}]}, {"operation": "SUM", "fields": ["trade_return_on_investment_roi"]}]}}}}}, {"DATABASE FIELD": "largest_win", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_largest_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_largest_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["transaction_largest_win"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_largest_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["trade_largest_win"]}}}}}, {"DATABASE FIELD": "largest_loss", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_largest_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "<", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_largest_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["transaction_largest_loss"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_largest_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["trade_largest_loss"]}}}}}, {"DATABASE FIELD": "smallest_win", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_smallest_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_smallest_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["transaction_smallest_win"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_smallest_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["trade_smallest_win"]}}}}}, {"DATABASE FIELD": "smallest_loss", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_smallest_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "<", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_smallest_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["transaction_smallest_loss"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_smallest_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["trade_smallest_loss"]}}}}}, {"DATABASE FIELD": "average_win", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_average_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_average_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": [{"operation": "IF", "condition": {"operation": ">", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_average_win", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_average_win"]}}}}}, {"DATABASE FIELD": "average_loss", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_average_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": "<", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_average_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": [{"operation": "IF", "condition": {"operation": "<", "field": "transaction_realized_pl_profit_loss", "value": 0}, "true_case": "transaction_realized_pl_profit_loss", "false_case": 0}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_average_loss", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_average_loss"]}}}}}, {"DATABASE FIELD": "highest_entry_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_highest_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_entry_price"}}}, "TRADE": {"DATABASE FIELD": "trade_highest_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["transaction_highest_entry_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_highest_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["trade_highest_entry_price"]}}}}}, {"DATABASE FIELD": "highest_exit_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_highest_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_exit_price"}}}, "TRADE": {"DATABASE FIELD": "trade_highest_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["transaction_highest_exit_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_highest_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MAX", "fields": ["trade_highest_exit_price"]}}}}}, {"DATABASE FIELD": "lowest_entry_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_lowest_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_entry_price"}}}, "TRADE": {"DATABASE FIELD": "trade_lowest_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["transaction_lowest_entry_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_lowest_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["trade_lowest_entry_price"]}}}}}, {"DATABASE FIELD": "lowest_exit_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_lowest_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_exit_price"}}}, "TRADE": {"DATABASE FIELD": "trade_lowest_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["transaction_lowest_exit_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_lowest_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "MIN", "fields": ["trade_lowest_exit_price"]}}}}}, {"DATABASE FIELD": "average_entry_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_average_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_entry_price"}}}, "TRADE": {"DATABASE FIELD": "trade_average_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_average_entry_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_average_entry_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_average_entry_price"]}}}}}, {"DATABASE FIELD": "average_exit_price", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_average_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "REFERENCE", "field": "transaction_exit_price"}}}, "TRADE": {"DATABASE FIELD": "trade_average_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_average_exit_price"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_average_exit_price", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_average_exit_price"]}}}}}, {"DATABASE FIELD": "capital_allocation_efficiency_cae", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_capital_allocation_efficiency_cae", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_allocated_cash", "value": 0}, "true_case": {"operation": "MULTIPLY", "fields": [{"operation": "DIVIDE", "fields": ["transaction_realized_pl_profit_loss", "transaction_allocated_cash"]}, 100]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_capital_allocation_efficiency_cae", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["transaction_capital_allocation_efficiency_cae", {"operation": "ABS", "fields": ["transaction_realized_pl_profit_loss"]}]}]}, {"operation": "SUM", "fields": [{"operation": "ABS", "fields": ["transaction_realized_pl_profit_loss"]}]}]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_capital_allocation_efficiency_cae", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "DIVIDE", "fields": [{"operation": "SUM", "fields": [{"operation": "MULTIPLY", "fields": ["trade_capital_allocation_efficiency_cae", {"operation": "ABS", "fields": ["trade_realized_pl_profit_loss"]}]}]}, {"operation": "SUM", "fields": [{"operation": "ABS", "fields": ["trade_realized_pl_profit_loss"]}]}]}}}}}, {"DATABASE FIELD": "book_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_book_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_book_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_book_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_book_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_book_value"]}}}}}, {"DATABASE FIELD": "market_value", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_market_value", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_market_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_market_value"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_market_value", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_market_value"]}}}}}, {"DATABASE FIELD": "free_cash_flow_fcf", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_free_cash_flow_fcf", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_free_cash_flow_fcf", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_free_cash_flow_fcf"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_free_cash_flow_fcf", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_free_cash_flow_fcf"]}}}}}, {"DATABASE FIELD": "current_ratio", "DATATYPE": "Number (Ratio)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_current_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_current_liabilities", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": ["transaction_current_assets", "transaction_current_liabilities"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_current_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_current_ratio"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_current_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_current_ratio"]}}}}}, {"DATABASE FIELD": "current_assets", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_current_assets", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_current_assets", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_current_assets"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_current_assets", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_current_assets"]}}}}}, {"DATABASE FIELD": "current_liabilities", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_current_liabilities", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_current_liabilities", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_current_liabilities"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_current_liabilities", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_current_liabilities"]}}}}}, {"DATABASE FIELD": "dividend_yield", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_dividend_yield", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_dividend_yield", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_dividend_yield"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_dividend_yield", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_dividend_yield"]}}}}}, {"DATABASE FIELD": "earnings_before_interest_and_taxes_ebit", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_earnings_before_interest_and_taxes_ebit", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_earnings_before_interest_and_taxes_ebit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_earnings_before_interest_and_taxes_ebit"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_earnings_before_interest_and_taxes_ebit", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_earnings_before_interest_and_taxes_ebit"]}}}}}, {"DATABASE FIELD": "earnings_before_interest_taxes_depreciation_and_amortization_ebitda", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_earnings_before_interest_taxes_depreciation_and_amortization_ebitda", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_earnings_before_interest_taxes_depreciation_and_amortization_ebitda", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_earnings_before_interest_taxes_depreciation_and_amortization_ebitda"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_earnings_before_interest_taxes_depreciation_and_amortization_ebitda", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_earnings_before_interest_taxes_depreciation_and_amortization_ebitda"]}}}}}, {"DATABASE FIELD": "free_float", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_free_float", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_free_float", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_free_float"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_free_float", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_free_float"]}}}}}, {"DATABASE FIELD": "interest_coverage_ratio", "DATATYPE": "Number (Ratio)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_interest_coverage_ratio", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_interest_coverage_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_interest_coverage_ratio"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_interest_coverage_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_interest_coverage_ratio"]}}}}}, {"DATABASE FIELD": "operating_margin", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_operating_margin", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_operating_margin", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_operating_margin"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_operating_margin", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_operating_margin"]}}}}}, {"DATABASE FIELD": "price_to_book_ratio_pb_ratio", "DATATYPE": "Number (Ratio)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_price_to_book_ratio_pb_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "IF", "condition": {"operation": ">", "field": "transaction_book_value", "value": 0}, "true_case": {"operation": "DIVIDE", "fields": ["transaction_market_value", "transaction_book_value"]}, "false_case": 0}}}, "TRADE": {"DATABASE FIELD": "trade_price_to_book_ratio_pb_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_price_to_book_ratio_pb_ratio"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_price_to_book_ratio_pb_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_price_to_book_ratio_pb_ratio"]}}}}}, {"DATABASE FIELD": "price_to_sales_ratio_ps_ratio", "DATATYPE": "Number (Ratio)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_price_to_sales_ratio_ps_ratio", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_price_to_sales_ratio_ps_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_price_to_sales_ratio_ps_ratio"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_price_to_sales_ratio_ps_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_price_to_sales_ratio_ps_ratio"]}}}}}, {"DATABASE FIELD": "quick_ratio", "DATATYPE": "Number (Ratio)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_quick_ratio", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_quick_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_quick_ratio"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_quick_ratio", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_quick_ratio"]}}}}}, {"DATABASE FIELD": "return_on_assets_roa", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_return_on_assets_roa", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_return_on_assets_roa", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_return_on_assets_roa"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_return_on_assets_roa", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_return_on_assets_roa"]}}}}}, {"DATABASE FIELD": "return_on_equity_roe", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_return_on_equity_roe", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_return_on_equity_roe", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_return_on_equity_roe"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_return_on_equity_roe", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_return_on_equity_roe"]}}}}}, {"DATABASE FIELD": "return_on_invested_capital_roic", "DATATYPE": "Number (Percentage)", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_return_on_invested_capital_roic", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_return_on_invested_capital_roic", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_return_on_invested_capital_roic"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_return_on_invested_capital_roic", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_return_on_invested_capital_roic"]}}}}}, {"DATABASE FIELD": "working_capital", "DATATYPE": "<PERSON><PERSON><PERSON><PERSON>", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_working_capital", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "SUBTRACT", "fields": ["transaction_current_assets", "transaction_current_liabilities"]}}}, "TRADE": {"DATABASE FIELD": "trade_working_capital", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_working_capital"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_working_capital", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_working_capital"]}}}}}, {"DATABASE FIELD": "circulating_supply", "DATATYPE": "Number", "SCOPES": {"TRANSACTION": {"DATABASE FIELD": "transaction_circulating_supply", "FORMULA": {"usr": 1, "api": 1, "sys": 0, "f1": 0, "f2": 0, "f3": 0, "f4": 0, "f5": 0}}, "TRADE": {"DATABASE FIELD": "trade_circulating_supply", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["transaction_circulating_supply"]}}}, "PORTFOLIO": {"DATABASE FIELD": "portfolio_circulating_supply", "FORMULA": {"usr": 0, "api": 0, "sys": 0, "f1": 1, "f2": 0, "f3": 0, "f4": 0, "f5": 0, "f1v": {"operation": "AVERAGE", "fields": ["trade_circulating_supply"]}}}}}]